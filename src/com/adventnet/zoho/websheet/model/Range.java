//// $Id$
/*
 * Range.java
 *
 * Created on April 13, 2007, 12:25 PM
 */


package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.CellReference.ReferenceMode;
import com.adventnet.zoho.websheet.model.ReEvaluate.RangeReEvaluateObject;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.ext.functions.FunctionUtil;
import com.adventnet.zoho.websheet.model.ext.functions.GCDLCM;
import com.adventnet.zoho.websheet.model.ext.functions.RangeFunctions;
import com.adventnet.zoho.websheet.model.ext.parser.ASTRangeNode;
import com.adventnet.zoho.websheet.model.paste.CutPaste;
import com.adventnet.zoho.websheet.model.paste.pasteblock.PasteCell;
import com.adventnet.zoho.websheet.model.paste.pasteblock.PasteColumnStyleToCell;
import com.adventnet.zoho.websheet.model.paste.pasteblock.PasteBlockHelper;
import com.adventnet.zoho.websheet.model.paste.pastetype.Paste;
import com.adventnet.zoho.websheet.model.paste.pastetype.PasteForSort;
import com.adventnet.zoho.websheet.model.util.*;
import com.singularsys.jep.EvaluationException;
import com.singularsys.jep.JepException;
import com.singularsys.jep.functions.Add;
import com.singularsys.jep.functions.Multiply;
import com.singularsys.jep.parser.ASTVarNode;
import com.singularsys.jep.parser.Node;
import com.zoho.sheet.chart.Chart;
import com.zoho.sheet.chart.CellProperties;
import com.zoho.sheet.chart.ChartUtils;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;  


/**
 *
 * <AUTHOR>
 */
public class Range implements ZArrayI, Iterable<ReadOnlyCell>
{
    public static final Logger LOGGER = Logger.getLogger(Range.class.getName());
    
    protected boolean isMarked = false;

    private boolean isFragmentsUpdated = false;
    
    // should be used only while reevaluateDependents.
    private RangeReEvaluateObject rangeReEvaluateObject;
    
    private Sheet sheet;

    private CellReference startCellReference = null;
    private CellReference endCellReference = null;
    
    //private String name;
   // private String rangeString; // this rangeString will be used in FillSeries

    /** Creates a new instance of Range
     * @param sheet
     * @param startRowIndex
     * @param startColIndex
     * @param endRowIndex
     * @param endColIndex */
    public Range(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex)
    {
        this.sheet = sheet;
        
        this.startCellReference = new CellReference(sheet.getCell(startRowIndex, startColIndex), true, true);
        this.endCellReference = new CellReference(sheet.getCell(endRowIndex, endColIndex), true, true);
    }

    public Range(Sheet sheet, CellReference startCellReference, CellReference endCellReference)
    {
        this.sheet = sheet;
        this.startCellReference = startCellReference;
        this.endCellReference = endCellReference;
    }

    public Range(Sheet sheet, Cell startCell, Cell endCell)
    {
        this(sheet, new CellReference(startCell,true,true), new CellReference(endCell, true, true));
    }
    
    public Range(Workbook book, String cellRangeAddress, String baseCellAddress, ReferenceMode referenceMode, boolean isFromParser) throws SheetEngineException
    {
        Sheet currSheet = null;
        CellReference dummyStartCellRef;
        CellReference dummyEndCellRef;
        try {
            int currRow = 0;
            int currCol = 0;

            if (baseCellAddress != null) {
                String[] sheetNameAndRef = CellUtil.getSheetNameAndRef(baseCellAddress);
                String sheetName = sheetNameAndRef[0];
                currSheet = book.getSheet(sheetName);

                String cellRef = sheetNameAndRef[1];

                currRow = CellUtil.getRow(cellRef);
                currCol = CellUtil.getColumn(cellRef);
            }

            String[] sheetNameAndRef = CellUtil.getSheetNameAndRef(cellRangeAddress);

            String sheetName = sheetNameAndRef[0];
            String cellRef = sheetNameAndRef[1];

            if (sheetName != null) {
                sheetName = sheetName.replace("'", "''");
                sheetName = CellUtil.getSheetNameEnclosed(sheetName);
                cellRangeAddress = sheetName + "." + cellRef;
            } else {
                cellRangeAddress = cellRef;
            }

//                }
            ////////////////////////////////////
            Node node = ExpressionImpl.generateNode(book, cellRangeAddress, currRow, currCol, false, isFromParser, ReferenceMode.A1);
            if (node instanceof ASTRangeNode) {
                if (currSheet == null) {
                    if (((ASTRangeNode) node).isSheetRelative()) {
                        LOGGER.log(Level.INFO, "The specified sheet in range is invalid : sheetName : {0}", cellRangeAddress);
                        throw new SheetEngineException("The specified sheet in range is invalid : sheetName : " + cellRangeAddress);//No I18N
                    } else {
                        currSheet = book.getSheet(0);
                    }
                }

                Range dummyRange = CellUtil.getRange((ASTRangeNode) node, currSheet, currRow, currCol);
                currSheet = dummyRange.getSheet();
                dummyStartCellRef = dummyRange.startCellReference;
                dummyEndCellRef = dummyRange.endCellReference;
            } else if (node instanceof ASTVarNode) {
                if (currSheet == null) {
                    if (((ASTVarNode) node).isSheetRelative()) {
                        LOGGER.log(Level.INFO, "The specified sheet in range is invalid : sheetName : {0}", cellRangeAddress);
                        throw new SheetEngineException("The specified sheet in range is invalid : sheetName : " + cellRangeAddress); //No I18N
                    } else {
                        currSheet = book.getSheet(0);
                    }
                }
                currSheet = ((ASTVarNode) node).getVarSheet(currSheet);
                dummyStartCellRef = ((ASTVarNode) node).getVarCellRef(currSheet, currRow, currCol);
                dummyEndCellRef = dummyStartCellRef;
            } else {
                throw new SheetEngineException("Invalid range address" +cellRangeAddress);//No I18N
            }
        } catch (SheetEngineException e) {
            throw e;
        } catch(EvaluationException e) {
            throw new SheetEngineException("Invalid range expression " + cellRangeAddress, e); // No I18N
        }

        this.sheet = currSheet;
        this.startCellReference = dummyStartCellRef;
        this.endCellReference = dummyEndCellRef;
    }


    public boolean isFragmentsUpdated()
    {
        return isFragmentsUpdated;
    }

    public void setFragmentsUpdated(boolean isFragmentsUpdated)
    {
        this.isFragmentsUpdated = isFragmentsUpdated;
    }


    public Sheet getSheet()
    {
        return sheet;
    }

    public CellReference getTopLeft()
    {
        return startCellReference;
    }

    public CellReference getBottomRight()
    {
        return endCellReference;
    }

    public int getStartRowIndex()
    {
        return startCellReference.getCell().getRowIndex();
    }

    public int getStartColIndex()
    {
        return startCellReference.getCell().getColumnIndex();
    }

    public int getEndRowIndex()
    {
        return endCellReference.getCell().getRowIndex();
    }

    public int getEndColIndex()
    {
        return endCellReference.getCell().getColumnIndex();
    }

    /**
     *
     * @param rowIndex this should be a relative rowIndex. So for Range B2:C3, to get value of B2, colIndex should be 0.
     * @param colIndex this should be a relative columnIndex.So for Range B2:C3, to get value of B2, colIndex should be 0.
     * @return
     */
    @Override
    public boolean isBound(int rowIndex, int colIndex)
    {
        rowIndex = this.getStartRowIndex() + rowIndex;
        colIndex = this.getStartColIndex() + colIndex;

        return !(colIndex < this.getStartColIndex() || colIndex > this.getEndColIndex()
                || rowIndex < this.getStartRowIndex() || rowIndex > this.getEndRowIndex());
    }

    /**
     *
     * @param absoluteRowIndex this should be an absolute rowIndex. So for Range B2:C3, to get value of B2, row should be 1.
     * @param absoluteColIndex this should be a absolute columnIndex.So for Range B2:C3, to get value of B2, col should be 1.
     * @return
     */
    public boolean isBoundAbsoluteReference(int absoluteRowIndex, int absoluteColIndex) {
        return isBound(absoluteRowIndex - this.getStartRowIndex(), absoluteColIndex - this.getStartColIndex());
    }

    private Value getValue(int relativeRowIndex, int relativeColIndex, boolean isWithPattern)
    {
        if (isBound(relativeRowIndex, relativeColIndex))
        {
            int absoluteRow = this.getStartRowIndex() + relativeRowIndex;
            int absoluteCol = this.getStartColIndex() + relativeColIndex;

            Value value = Value.EMPTY_VALUE;
            if (absoluteRow <= this.getSheet().getUsedRowIndex() && absoluteCol <= this.getSheet().getUsedColumnIndex())
            {
                Cell tempCell = this.getSheet().getReadOnlyCellFromShell(absoluteRow, absoluteCol).getCell();
                if (tempCell != null)
                {
                    value = tempCell.getValue();
                    if(isWithPattern && value.getType() != Type.UNDEFINED)
                    {
                        value = new ValueWithPattern((ValueI) tempCell.getValue(), ((CellImpl)tempCell).getPattern(2));
                    }
                }
            }
            return value;
        }

        return Value.getInstance(Type.ERROR, Cell.Error.NA);
    }

    /**
     *
     * @param relativeRowIndex this should be a relative rowIndex. So for Range B2:C3, to get value of B2, row should be 0.
     * @param relativeColIndex this should be a relative columnIndex. So for Range B2:C3, to get value of B2, col should be 0.
     * @return
     */
    @Override
    public Value getValue(int relativeRowIndex, int relativeColIndex)
    {
        return getValue(relativeRowIndex, relativeColIndex, false);
    }

    @Override
    public Value getValueWithPattern(int relativeRowIndex, int relativeColIndex)
    {
        return getValue(relativeRowIndex, relativeColIndex, true);
    }

    /**
     *
     * @param absoluteRowIndex this should be an absolute rowIndex. So for Range B2:C3, to get value of B2, row should be 1.
     * @param absoluteColIndex this should be a absolute columnIndex.So for Range B2:C3, to get value of B2, col should be 1.
     * @return
     */
    public Object getValueForAbsoluteReference(int absoluteRowIndex, int absoluteColIndex) {
        return getValue(absoluteRowIndex - this.getStartRowIndex(), absoluteColIndex - this.getStartColIndex());
    }


    public Cell[] getRange()
    {
        int startRowIndex = this.getStartRowIndex();
        int startColIndex = this.getStartColIndex();
        int endRowIndex = this.getEndRowIndex();
        int endColIndex = this.getEndColIndex();

	int rowIndex = this.getSheet().getRowNum() -1;
	int colIndex = this.getSheet().getColNum() -1;

	//startRowIndex = startRowIndex > rowIndex?rowIndex:startRowIndex;
	endRowIndex = endRowIndex > rowIndex?rowIndex:endRowIndex;

    //startColIndex = startColIndex > colIndex?colIndex:startColIndex;
    endColIndex = endColIndex > colIndex?colIndex:endColIndex;

	if(startRowIndex > endRowIndex || startColIndex > endColIndex)
	{
	    return new Cell[0];
	}

        int totalRows = (endRowIndex - startRowIndex) + 1;
        int totalCols = (endColIndex - startColIndex) + 1;
        int counter = 0;

        Cell cell[] = new Cell[totalRows * totalCols];


        for(int i = startRowIndex; i <= endRowIndex; i++)
        {
            for(int j = startColIndex; j <= endColIndex; j++)
            {
                cell[counter] = sheet.getCell(i, j);
                counter++;
            }
        }

        return cell;
    }
    
        @Override
    public void addArrayValues(Stack stack)
    {
        addRangeValues(stack);
        
    }

    public void addRangeValues(Stack stack)
    {
        //List<Value> rangeValues = getRangeReEvaluateObject().getRangeValues();
        //if(rangeValues.isEmpty())
        //{
            RangeIterator rIterator = new RangeIterator(this, RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, false, false, true, false, true);
            while(rIterator.hasNext())
            {
                ReadOnlyCell rCell = rIterator.next();
                Cell cell = rCell.getCell();
                Value cellValue = cell == null ? null : cell.getValue();
                for(int r = 0; r < rCell.getRowsRepeated(); r++)
                {
                    for(int c = 0; c < rCell.getColsRepeated(); c++)
                    {
                        stack.push(cellValue);
                    }
                }
            }
        //}
        //stack.addAll(rangeValues);
    }


    public static Range getRange(Sheet sheet, String startCellName, String endCellName, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex) throws EvaluationException
    {
	    String sSheetName = "";
	    String eSheetName = "";

	    // separate the sheet name from the cell name
	    if(startCellName.contains("."))
	    {
		sSheetName = startCellName.substring(0, startCellName.indexOf("."));
		startCellName = startCellName.substring(startCellName.indexOf(".")+1);
	    }
	    if(endCellName.contains("."))
	    {
		eSheetName = endCellName.substring(0, endCellName.indexOf("."));
		endCellName = endCellName.substring(endCellName.indexOf(".")+1);
	    }

	    String sheetName = null;
	    if( !(sSheetName.equalsIgnoreCase(eSheetName)) && !("".equals(eSheetName)) && !("".equals(sSheetName)) )
	    {
		LOGGER.info("The range is spread accross Sheets, so its a error.");//No I18N
		throw Cell.Error.VALUE.getThrowableObject();
	    }else
	    {
		sheetName = sSheetName;
	    }


	    if(sSheetName.startsWith("$"))
	    {
		sheetName = sheetName.substring(1); // lets trim the $
	    }
	    if(sSheetName.contains("'"))
	    {
		sheetName = sheetName.replaceAll("'", "");
	    }

	    if(sheetName != null && !"".equals(sheetName))
	    {
		// Get the sheet which is specified in the Range of Formula
		sheet = sheet.getWorkbook().getSheet(sheetName);
		if(sheet == null)
		{
		    LOGGER.info("Sheet name given in Range not available.");//No I18N
		    throw Cell.Error.NAME.getThrowableObject();
		}
	    }

	    if(!CellUtil.isCellRangeBound(startCellName) && !CellUtil.isCellRangeBound(endCellName))
	    {
		LOGGER.info("Cell reference is beyond sheet size");//No I18N
		throw Cell.Error.NAME.getThrowableObject();
	    }

            Cell startCell = sheet.getCell(startRowIndex, startColIndex);
            Cell endCell = sheet.getCell(endRowIndex, endColIndex);

            boolean isStartColumnRelative  =  CellUtil.isColumnRelative(startCellName);
            boolean isStartRowRelative = CellUtil.isRowRelative(startCellName);

            boolean isEndColumnRelative  =  CellUtil.isColumnRelative(endCellName);
            boolean isEndRowRelative = CellUtil.isRowRelative(endCellName);

	    CellReference startCellReference = new CellReference(startCell, isStartColumnRelative, isStartRowRelative);
            CellReference endCellReference = new CellReference(endCell, isEndColumnRelative, isEndRowRelative);

            Range range = new Range(sheet, startCellReference, endCellReference);

	    return range;

    }

    public String getCellRangeAddress()
    {
	StringBuilder sBuff = new StringBuilder();
	String sheetName = this.getSheet().getName();
	sheetName= CellUtil.getSheetNameEnclosed(sheetName);
	sBuff.append("$").append(sheetName).append(".");
	sBuff.append(this.getTopLeft().toString()).append(":").append(".");
	sBuff.append(this.getBottomRight().toString());
	return sBuff.toString();
    }
    
    public String getSimpleRangeAddress()
    {
        StringBuilder sBuff = new StringBuilder();	
	sBuff.append(this.getStartRowIndex()).append(":");
        sBuff.append(this.getStartColIndex()).append(":");
        sBuff.append(this.getEndRowIndex()).append(":");
        sBuff.append(this.getEndColIndex());
	return sBuff.toString();
    }

    public String getSimpleRangeAddressInA1()
    {
        StringBuilder sBuff = new StringBuilder();
        sBuff.append(this.getTopLeft().toString()).append(":");
        sBuff.append(this.getBottomRight().toString());
        return sBuff.toString();
    }

    ////////////////////////////////////////////////////////////////////////


    @Override
    public Iterator<ReadOnlyCell> iterator() {
        return iterator(RangeIterator.IterationStartPositionEnum.TOP_LEFT, false, true, false);
    }
    
    public Iterator<ReadOnlyCell> iterator(RangeIterator.IterationStartPositionEnum startPosition, boolean skipHiddenRows, boolean isTillUsedIndex, boolean isRepeatRedundant){
        return new RangeIterator(this, startPosition, skipHiddenRows, false, false, isTillUsedIndex, isRepeatRedundant);
    }
    
    public Iterator<Value> valueWithPatternIterator(boolean skipHiddenRows, boolean isTillUsedIndex, boolean isRepeatRedundant){
        return new RangeValueIterator(this, skipHiddenRows, isTillUsedIndex, isRepeatRedundant);
    }

    public Iterator<Value> valueIterator(boolean skipHiddenRows, boolean isTillUsedIndex, boolean isRepeatRedundant){
        return new RangeValueIterator(this, skipHiddenRows, isTillUsedIndex, isRepeatRedundant, false);
    }
    
//    private static final Comparator<CellHolder> NUMBER_VALUE_ORDER = new NumberValueComparator();
//    private static final Comparator<CellHolder> DATE_VALUE_ORDER = new DateValueComparator();
    //////////////////////////////////////////////////////////

    private static boolean isPicklistCell(Map<Integer, List<DataRange>> picklistMap, int row, int col) {
        for(Map.Entry<Integer,List<DataRange>> entry : picklistMap.entrySet()) {
            List<DataRange> rangeList = entry.getValue();
            for(DataRange range : rangeList) {
                if (range.isCellInRange(range.getAssociatedSheetName(), row, col)) {
                    return true;
                }
            }
        }
        return false;
    }
    
    public static void pasteCellToCellForSort(WorkbookContainer container, Range sortRange, Range srcRange, Range destRange, Cell src, Cell destCell, Set<Integer> modifiedPicklists, Map<Integer, List<DataRange>> oldPicklistRangeMap, PasteBlockHelper pasteBlockHelper){
        Paste pasteCell= new PasteForSort(new PasteCell(container, container, new ReadOnlyCell(src.getRow().getSheet(), src, src.getColumnIndex(), src.getRowIndex(), src.getRow().getRowsRepeated(), src.getColsRepeated()), destCell, PasteCell.Type.SORT, null, pasteBlockHelper));

        /* To check and change PicklistRanges before picklist cells are copied.*/
        Sheet destSheet = destRange.getSheet();
        Sheet srcSheet = srcRange.getSheet();
        /*Some cells have PicklistValue even though they are not Picklist Cells. This happens for formula cells that refer to
         * the Picklist cell. So adding isPicklistCell check to avoid treating those cells as Picklist Cells.
         * This isPicklistCell check can be removed once that issue is fixed.
         * Using a cloned Map because while sorting the original might get modified */
        if(src.getValue() instanceof PicklistValue && isPicklistCell(oldPicklistRangeMap, src.getRowIndex(), src.getColumnIndex())) {
            if(!(destCell.getValue() instanceof PicklistValue)) {
                int picklistID = ((PicklistValue) src.getValue()).getPicklist().getId();
                List<DataRange> picklistRanges = srcSheet.getPicklistRangeMap().get(picklistID);
                modifiedPicklists.add(picklistID);
                if(picklistRanges != null) {
                    picklistRanges.add(new DataRange(destSheet.getAssociatedName(), destCell.getRowIndex(), destCell.getColumnIndex(), destCell.getRowIndex(), destCell.getColumnIndex()));
                }
            }
        } else if(destCell.getValue() instanceof PicklistValue) {
            PicklistUtil.removePicklistRange(destSheet,new DataRange(destSheet.getAssociatedName(),destCell.getRowIndex(),destCell.getColumnIndex(),destCell.getRowIndex(),destCell.getColumnIndex()));
        }

        if(src.getCellPicklists() != null && !src.getCellPicklists().isEmpty()) {
            if(destCell.getCellPicklists() == null) {
                for(CellPicklist cellPicklist: src.getCellPicklists()) {
                    List<DataRange> sourceRanges = cellPicklist.getPicklist().getSourceRanges();
                    modifiedPicklists.add(cellPicklist.getPicklist().getId());
                    if(sourceRanges != null) {
                        sourceRanges.add(new DataRange(destSheet.getAssociatedName(), destCell.getRowIndex(), destCell.getColumnIndex(), destCell.getRowIndex(), destCell.getColumnIndex()));
                    }
                }
            }
        }
        else if(destCell.getCellPicklists() != null) {
            PicklistUtil.removePicklistSourceRange(destSheet.getWorkbook(), new DataRange(destSheet.getAssociatedName(),destCell.getRowIndex(),destCell.getColumnIndex(),destCell.getRowIndex(),destCell.getColumnIndex()));
        }

        pasteCell.paste();
        
        if(src.getStyleName()==null || ((CellImpl)src).getPattern(1)==null){
            Paste pasteColStyleToCell = new PasteForSort(new PasteColumnStyleToCell(src.getRow().getSheet().getReadOnlyColumnHeader(src.getColumnIndex()), destCell, src, null, pasteBlockHelper));
            pasteColStyleToCell.paste();
        }
        
        if(src.isFormula()){
            try{
                Expression srcExpr= src.getExpression();
                Expression newExpr= getTransposedExpressionForSort(srcExpr, sortRange, srcRange, destRange, src, destCell);
                destCell.setExpression(newExpr, true);
            }
            catch(Exception e){
                LOGGER.log(Level.INFO, "################ JepException in formula : ", e);//No I18N
            }
        }
    }
    
    public static Expression getTransposedExpressionForSort(Expression srcExpr, Range sortRange, Range srcRange, Range destRange, Cell srcCell, Cell destCell) throws JepException{
        Node clonedTree= Workbook.getDeepCopyVisitor().deepCopy(srcExpr.getNode());
        transposeTreeForSort(clonedTree, sortRange, srcRange, destRange, srcCell);
        Expression transposedExpr= new ExpressionImpl(clonedTree);
        return transposedExpr;
    }
    
    public static void transposeTreeForSort(Node tree, Range sortRange, Range srcRange, Range destRange, Cell srcCell) throws JepException{
        if(tree instanceof ASTVarNode){
            CutPaste.transposeVarNodeForSort((ASTVarNode)tree, srcCell, srcRange, destRange, sortRange);
        }
        else if(tree instanceof ASTRangeNode){
            CutPaste.transposeRangeTree((ASTRangeNode)tree, srcCell, srcRange, destRange, false, false, true);
        }
        else{
            for(int i=0; i<tree.jjtGetNumChildren(); i++){
                Node childTree= tree.jjtGetChild(i);
                try{
                    transposeTreeForSort(childTree, sortRange, srcRange, destRange, srcCell);
                }
                catch(JepException e){}
            }
        }
    }

    public List<Cell> getExclusiveDirectDependents(boolean isUsedIndex)
    {
	return getDirectDependents(true, isUsedIndex);
    }
    
    public List<Cell> getDirectDependents(boolean isExcludeMemberCells, boolean isUsedIndex){
        
        List<Cell> directDependents = new ArrayList<>();

        // Add Range Dependents.
        Workbook workbook = this.getSheet().getWorkbook();
        List<Cell> rangeDeps = workbook.getDependentsFromRange(this);
        for(Cell dep : rangeDeps)
        {
            if(!((CellImpl)dep).isMarked && isExcludeMemberCells ? !this.isMember(dep) : true)
            {
                ((CellImpl)dep).isMarked = true;
                directDependents.add(dep);
            }
        }

        int startRow = this.getStartRowIndex();
        int startCol = this.getStartColIndex();
        int endRow = this.getEndRowIndex();
        int endCol = this.getEndColIndex();
	for(int r = startRow; r <= endRow; r++)
	{
            for(int c = startCol; c <= endCol; c++)
            {
                Cell cell = this.getSheet().getCellReadOnly(r, c);
                if(cell != null)
                {
                    Collection<Cell> cellDeps = cell.getDependents();
                    for(Cell dep : cellDeps)
                    {
                        if(!((CellImpl)dep).isMarked && isExcludeMemberCells ? !this.isMember(dep) : true)
                        {
                            ((CellImpl)dep).isMarked = true;
                            directDependents.add(dep);
                        }
                }
            }
        }
        }

        for(Cell dep : directDependents)
        {
            ((CellImpl)dep).isMarked = false;
        }

	return directDependents;
    }
    
    public void updateChartReGenStatus(boolean checkForHiddenCell)
    {
        ////// check if chart require any regen
        Workbook workbook = this.getSheet().getWorkbook();

        if(workbook != null)
        {
            Map<String, Map<String, Chart>> chartMap = workbook.getChartMap();

            if(chartMap != null)
            {
                DataRange dataRange = this.toDataRange();
                Collection<Chart> chartsList;

                for(Sheet sheet : workbook.getSheetList())
                {
                    Map<String, Chart> sheetChartMap = chartMap.get(sheet.getAssociatedName());

                    if(sheetChartMap != null)
                    {
                        chartsList = sheetChartMap.values();

                        for(Chart chart : chartsList)
                        {
                            if(chart != null && chart.isInDataRanges(dataRange) && (!checkForHiddenCell || !chart.getisIncludeHiddenCells()))
                            {
                                chart.setReGenRequired(true);

                                if(ChartUtils.isChartPublished(chart.getChartId()))
                                {
                                    try
                                    {
                                        ChartUtils.updateChartsGodown(chart.getDocumentId(), chart.getSheetName(), chart.getChartId(), chart.getPublicChartName());
                                    }
                                    catch(Exception e)
                                    {
                                        LOGGER.log(Level.INFO, "[CHART] Exception in updating charts godown {0}", e);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }	

    public static List<ReadOnlyCell> getReadOnlyRange(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, boolean skipHiddenRows)
    {
        endRowIndex = Math.min(endRowIndex, sheet.getUsedRowIndex());
        endColIndex = Math.min(endColIndex, sheet.getUsedColumnIndex());
        List<ReadOnlyCell> cellsList = new ArrayList<>();
        int rowIndex = startRowIndex;
        while(rowIndex <= endRowIndex)
        {
            int colIndex = startColIndex;
            int rowsRepeated = 1;

            Row oRow = sheet.getRowReadOnly(rowIndex);
            if(oRow == null)
            {
                Row prevRow = sheet.getPreviousRow(rowIndex);
                if(prevRow != null && prevRow.getRowsRepeated() > (rowIndex - prevRow.getRowIndex()))
                {
                    oRow = prevRow;
                    rowsRepeated = prevRow.getRowsRepeated() - (rowIndex - prevRow.getRowIndex());
                }
            }
            else
            {
                rowsRepeated = oRow.getRowsRepeated();
            }

            if(!(skipHiddenRows && !RowUtil.isVisibleRow(oRow)))
            {
                if(oRow != null)
                {
                    if((rowIndex + rowsRepeated) > endRowIndex)
                    {
                        rowsRepeated = (endRowIndex - rowIndex) + 1;
                    }
                    while(colIndex <= endColIndex)
                    {
                        int colsRepeated = 1;
                        Cell cell = oRow.getCellReadOnly(colIndex);
                        if(cell == null)
                        {
                            Cell prevCell = null;
                            for(int j = (colIndex - 1); j >= 0; j--)
                            {
                                prevCell = oRow.getCellReadOnly(j);
                                if(prevCell != null)
                                {
                                    break;
                                }
                            }
                            if(prevCell != null && prevCell.getColsRepeated() > (colIndex - prevCell.getColumnIndex()))
                            {
                                cell = prevCell;
                                colsRepeated = prevCell.getColsRepeated() - (colIndex - prevCell.getColumnIndex());
                            }
                        }
                        else
                        {
                            colsRepeated = cell.getColsRepeated();
                        }

                        if((colIndex + colsRepeated) > endColIndex)
                        {
                            colsRepeated = (endColIndex - colIndex) + 1;
                        }

                        // create the ReadOnlyCell
                        cellsList.add(new ReadOnlyCell(sheet, cell, rowIndex, colIndex, rowsRepeated, colsRepeated));
                        colIndex += colsRepeated;
                    }
                }
                else
                {
                    // when row is null
                    int colsRepeatedTemp = (endColIndex - colIndex) + 1;
                    cellsList.add(new ReadOnlyCell(sheet, null, rowIndex, colIndex, rowsRepeated, colsRepeatedTemp));
                }
            }
            rowIndex += rowsRepeated;
        }

        return cellsList;
    }

    @Override
    public int getSize()
    {
	return (this.getRowSize()) * this.getColSize();
    }

    @Override
    public int getRowSize()
    {
	return (this.getEndRowIndex() - this.getStartRowIndex()) + 1;
    }

    @Override
    public int getColSize()
    {
        return (this.getEndColIndex() - this.getStartColIndex()) +1;
    }

    @Override
    public String toString()
    {
        return this.getTopLeft().getReference()+":"+getBottomRight().getReference();
    }

    // returns the string without sheet name. Used for changing formula while Insert/delete row/column and copy/paste cases.
    public String getRangeString()
    {
        return this.getTopLeft().getReference()+":"+getBottomRight().getReference();
    }
    
    public String getRangeStringForClient()
    {
        StringBuilder rangeString = new StringBuilder();
        
        rangeString.append(CellUtil.getSheetNameEnclosed(sheet.getName())).append(".");
        
        if(this.getStartRowIndex() == 0 && this.getEndRowIndex() >= Utility.MAXNUMOFROWS - 1)
        {
            rangeString.append(this.getTopLeft().getColReference()).append(":").append(getBottomRight().getColReference());
        }
        else if(this.getStartColIndex() == 0 && this.getEndColIndex() >= Utility.MAXNUMOFCOLS - 1)
        {
            rangeString.append(this.getTopLeft().getRowReference()).append(":").append(getBottomRight().getRowReference());
        }
        else{
            rangeString.append(this.getRangeString());
        }
        
        return rangeString.toString();
    }

    public String toAbsoluteA1String()
    {
        StringBuilder rangeString = new StringBuilder();

        rangeString.append(CellUtil.getSheetNameEnclosed(sheet.getName())).append(".");

        if(this.getStartRowIndex() == 0 && this.getEndRowIndex() == Utility.MAXNUMOFROWS - 1)
        {
            rangeString.append(this.getTopLeft().getColReference()).append(":").append(getBottomRight().getColReference());
        }
        else if(this.getStartColIndex() == 0 && this.getEndColIndex() == Utility.MAXNUMOFCOLS - 1)
        {
            rangeString.append(this.getTopLeft().getRowReference()).append(":").append(getBottomRight().getRowReference());
        }
        else{
            rangeString.append(this.getTopLeft().getReference()).append(":").append(getBottomRight().getReference());
        }

        return rangeString.toString();
    }

    public String getCompleteRangeString()
    {
        return sheet.getName()+"."+getRangeString();
    }
    
    public String getCompleteRangeStringForXML()
    {
        return CellUtil.getSheetNameEnclosed(sheet.getName())+"."+getRangeString();
    }
    

//    public String getPivotCompitableRangeStringForXML()
//    {
////        String sheetName = sheet.getName();
////        if(sheetName.contains(" ")){
////        	sheetName = "'"+sheetName+"'";
////        }
//        String sheetName = CellUtil.getSheetNameEnclosed(sheet.getName());
//        return sheetName+"."+getTopLeft().getReference()+":"+sheetName+"."+getBottomRight().getReference();
//    }
    public DataRange toDataRange()
    {
	 return new DataRange(this.getSheet().getAssociatedName(),
		    this.getStartRowIndex(), this.getStartColIndex(), this.getEndRowIndex(), this.getEndColIndex());
    }

    ///////////////////////////////////////////////////////////////
    
    public boolean isMember(Cell cell)
    {
        return isMember(cell.getRow().getSheet(), cell.getRowIndex(), cell.getColumnIndex());
    }

    public boolean isMember(Sheet inSheet, int cellRow, int cellCol)
    {
	return !(inSheet != this.getSheet() ||
                cellRow < this.getStartRowIndex() || cellRow > this.getEndRowIndex() ||
                cellCol < this.getStartColIndex() || cellCol > this.getEndColIndex());
    }

    @Override
    public boolean equals(Object obj)
    {
	if(this == obj) // hashcode check done
        {
            return true;
        }

        if(obj != null && obj instanceof Range)
        {
            Range range = (Range)obj;
            if(this.getSheet() == range.getSheet() &&  
               this.getStartRowIndex() == range.getStartRowIndex() &&
               this.getStartColIndex() == range.getStartColIndex() &&
               this.getEndRowIndex() == range.getEndRowIndex() &&
               this.getEndColIndex() == range.getEndColIndex())
            {
                    return true;
            }
        }

        return false;
    }

    @Override
    public int hashCode() {
        int hash = 7;
        hash = 53 * hash + this.sheet.hashCode();
        hash = 53 * hash + this.startCellReference.getCell().hashCode();
        hash = 53 * hash + this.endCellReference.getCell().hashCode();
        return hash;
    }
    
    public List<List<Object>> getRangeValues(boolean isIncludeHiddenCells, boolean isExcludeEmptyRows)
    {
        if(this.getStartRowIndex() > this.getSheet().getUsedRowIndex() || this.getStartColIndex() > this.getSheet().getUsedColumnIndex())
        {
            return null;
        }
        List<List<Object>> data = new ArrayList<>();
        int skippedHiddenRowsCount = 0;
        int LastRowIndex = this.getStartRowIndex() - 1;
        RangeIterator rangeIterator = new RangeIterator(this, RangeIterator.IterationStartPositionEnum.TOP_LEFT, !isIncludeHiddenCells, isExcludeEmptyRows, !isIncludeHiddenCells, true, false, true);
        while(rangeIterator.hasNext())
        {
            ReadOnlyCell rCell = rangeIterator.next();
            if(rCell.getRowIndex() - LastRowIndex > 1)
            {
                skippedHiddenRowsCount += (rCell.getRowIndex() - LastRowIndex - 1);
            }
            LastRowIndex = rCell.getRowIndex() + rCell.getRowsRepeated() - 1;
            CellProperties cp = CellProperties.getEmptycellproperties();
            Cell cell = rCell.getCell();
            if(cell != null)
            {
                int mergeRowSpan =  rCell.getSheet().getMergeCellSpans(cell)[0];
                int mergeColSpan =  rCell.getSheet().getMergeCellSpans(cell)[1];
                cp = new CellProperties();
                cp.set(CellProperties.VALUE, cell.getValue().getValue());
                cp.set(CellProperties.CELLCONTENT, cell.getContent());
                cp.set(CellProperties.CELLSYMBOL, (cell.getContentType() == Type.PERCENTAGE) ? "%" : (cell.getContentType() == Type.CURRENCY)  ? LocaleUtil.getCurrencySymbol(cell.getCurrencyCode()) : "");
                cp.set(CellProperties.MERGECELL, mergeRowSpan > 1);
                cp.set(CellProperties.ROWSPAN, mergeRowSpan);
                cp.set(CellProperties.CELLTYPE,cell.getContentType());
                cp.set(CellProperties.COLSPAN, mergeColSpan);
            }
            
            int rIndex = rCell.getRowIndex() - this.getStartRowIndex() - skippedHiddenRowsCount;      
            for(int i = 0; i < rCell.getRowsRepeated(); i++)
            {
            	List<Object> columnList = null;
            	if(data.size()>rIndex && data.get(rIndex) != null) {
            		columnList = data.get(rIndex);
            	}else{
            		columnList = new ArrayList<>();
            	}
                for(int j = 0; j < rCell.getColsRepeated(); j++)
                {
                	columnList.add(cp);
                 }
                if(data.size()<=rIndex){
                	data.add(columnList);
                }
              rIndex++;
            }
        }
        return data;
    }



    public boolean isEmptyRange()
    {
        return RangeUtil.isBlankRange(this.getSheet(), this.getStartRowIndex(), this.getStartColIndex(), this.getEndRowIndex(), this.getEndColIndex(), true);
    }

    public static void swap(Range srcRange, Range destRange)
    {
        int srcRowSize = srcRange.getRowSize();
        int srcColSize = srcRange.getColSize();

        int destStartRowIndex = destRange.getStartRowIndex();
        int destStartColIndex = destRange.getStartColIndex();

        int srcStartRowIndex = srcRange.getStartRowIndex();
        int srcStartColIndex = srcRange.getStartColIndex();

        if(srcRowSize != destRange.getRowSize() || srcColSize != destRange.getColSize())
        {
            destRange = new Range(destRange.getSheet(), destStartRowIndex, destStartColIndex, destStartRowIndex + srcRowSize - 1, destStartColIndex + srcColSize - 1);
        }

        for(int row = 0; row < srcRowSize; row++)
        {
            for(int col = 0; col < srcColSize; col++)
            {
                Cell srcCell = srcRange.getSheet().getCell(srcStartRowIndex + row, srcStartColIndex + col);
                Cell destCell = destRange.getSheet().getCell(destStartRowIndex + row, destStartColIndex + col);

                Cell destCellClone = destCell.clone();

                //Commenting this code since copyFrom() has been removed and new paste implementations are being used.
                //Should change this to accustom to the new paste interface/implementation when being deployed.
//                destCell.copyFrom(srcRange, srcCell, destRange, PasteSpecialEnum.ALL, false, true, false, false);
//                srcCell.copyFrom(destRange, destCellClone, srcRange, PasteSpecialEnum.ALL, false, true, false, false);
            }
        }
    }
    
    public static Object getCumulativeValues(RangeFunctions.RangeValType type, Object val, Object result, SpreadsheetSettings spreadsheetSettings) throws EvaluationException
    {
        if(result == null)
        {
            return ((val instanceof Value) && ((Value)val).getValue() == null) ? null : val;
        }
        switch(type)
        {
            case SUM:
            case COUNT: 
            case COUNTA: 
            case COUNTBLANK:
            case SUMOFSQ:
                Add addFun = new Add();
                return addFun.add(val, result, spreadsheetSettings);
            case MIN: 
            case MINA:
                if(result ==  null && val == null) {
                    return null;
                } else if(result == null) {
                    return val;
                } else if(val == null){
                    return result;
                }
                double t1 = FunctionUtil.objectToNumber(result).doubleValue();
                double t2 = FunctionUtil.objectToNumber(val).doubleValue();
                return Math.min(t1, t2);
            case MAX: 
            case MAXA:
                if(result ==  null && val == null) {
                    return null;
                } else if(result == null) {
                    return val;
                } else if(val == null){
                    return result;
                }
                t1 = FunctionUtil.objectToNumber(result).doubleValue();
                t2 = FunctionUtil.objectToNumber(val).doubleValue();
                return Math.max(t1, t2);
            case PRODUCT: 
                Multiply mulFun = new Multiply();
                return val == null ? result : mulFun.mul(val, result, spreadsheetSettings);
            case GCD: 
                GCDLCM temp = new GCDLCM(GCDLCM.GCD);
                return temp.gcd_lcm(val, result, spreadsheetSettings);
            case LCM: 
                temp = new GCDLCM(GCDLCM.LCM);
                return temp.gcd_lcm(val, result, spreadsheetSettings);
        }
        return null;
    }
    
    public static class TempConditionalStyleObject{
        private final List<Range> dataRanges;
        private final ConditionalStyleObject cso;
        
        public TempConditionalStyleObject(List<Range> dataRanges, ConditionalStyleObject cso) {
            this.dataRanges = dataRanges;
            this.cso = cso;
        }

        public List<Range> getDataRanges() {
            return dataRanges;
        }

        public ConditionalStyleObject getConditionalStyleObject() {
            return cso;
        }
    }
    
    public RangeReEvaluateObject getRangeReEvaluateObject()
    {
        if(this.rangeReEvaluateObject == null || this.rangeReEvaluateObject.getReEvaluate() == null ||
                    this.rangeReEvaluateObject.getReEvaluate() != this.getSheet().getWorkbook().getReEvaluate())
        {
            rangeReEvaluateObject = new RangeReEvaluateObject(this.getSheet().getWorkbook().getReEvaluate());
        }
        return rangeReEvaluateObject;
    }

    @Override
    public boolean isMadeofRange() {
        return true;
    }
}
