package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.style.BorderProperties;
import com.adventnet.zoho.websheet.model.style.CellStyle;
import com.adventnet.zoho.websheet.model.style.Shadow;
import com.adventnet.zoho.websheet.model.style.TextStyle;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSColor;
import com.adventnet.zoho.websheet.model.style.ZSColor.ZSThemeColor;
import com.adventnet.zoho.websheet.model.style.fill.*;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSColorScheme;
import com.adventnet.zoho.websheet.model.util.EngineConstants;

import java.util.*;



public class SlicerStyleContainer {

    private static final List< Map<Slicer.SlicerStyle,CellStyle>> SLICER_STYLE_LIST= new ArrayList<>();
    static {
        //   STYLE1
        CellStyle wholeTableStyle = new CellStyle();
        Fill gradient = GradientFill.getLinearInstance(90.0, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.95), ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.89));
        wholeTableStyle.setProperty(CellStyle.Property.FILL, gradient);
        BorderProperties borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.80), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        CellStyle headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, -0.25));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        CellStyle itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.67));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.67), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //ITEM OFF STYLE
        CellStyle itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.80));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.80), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);
        Map<Slicer.SlicerStyle,CellStyle> styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE2
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.75));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.75), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.90), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE3
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1,0.62), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.61));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.61), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);


        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.96));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.96), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE4
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.97));
        borderProperties = BorderProperties.getInstance("solid",ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.49));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("dashed", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE5
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.98));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.90), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, -0.25));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        gradient = GradientFill.getLinearInstance(90.0, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.93), ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.73));
        itemOnStyle.setProperty(CellStyle.Property.FILL, gradient);
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.59), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.90), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE6
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.20));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.20), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, -0.07));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.0), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);

        //   STYLE7
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.21));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.21), 1);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.77));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.21));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.21), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.17));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.17), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);
        //   STYLE8
        wholeTableStyle = new CellStyle();
        wholeTableStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
//        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.82), 1);
//        wholeTableStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
//        wholeTableStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
//        wholeTableStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
//        wholeTableStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //HEADER STYLE
        headerStyle = new CellStyle();
        headerStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(ZSColorScheme.Colors.TEXT1,0.00));
//        headerStyle.setProperty(TextStyle.Property.FONTSIZE, "13");

        //ITEM ON STYLE
        itemOnStyle = new CellStyle();
        itemOnStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.82));
        itemOnStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.82), 1);
        itemOnStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOnStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        //ITEM OFF STYLE
        itemOffStyle = new CellStyle();
        itemOffStyle.setProperty(CellStyle.Property.BACKGROUNDCOLOR, ZSColor.getInstance(ZSColorScheme.Colors.BACKGROUND1,0));
        itemOffStyle.setProperty(TextStyle.Property.COLOR, ZSColor.getInstance(EngineConstants.TEXTCOLOR_AUTOMATIC));
        borderProperties = BorderProperties.getInstance("solid", ZSColor.getInstance(ZSColorScheme.Colors.ACCENT1, 0.62), 1);
        itemOffStyle.setProperty(CellStyle.Property.BORDERLEFT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERRIGHT, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERBOTTOM, borderProperties);
        itemOffStyle.setProperty(CellStyle.Property.BORDERTOP, borderProperties);

        styleMap = new HashMap<>();
        styleMap.put(Slicer.SlicerStyle.WHOLETABLESTYLE,wholeTableStyle);
        styleMap.put(Slicer.SlicerStyle.HEADERSTYLE,headerStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMONSTYLE,itemOnStyle);
        styleMap.put(Slicer.SlicerStyle.ITEMOFFSTYLE,itemOffStyle);

        SLICER_STYLE_LIST.add(styleMap);
    }
    private static CellStyle replaceAccentInStyle(CellStyle cellStyle, ZSColorScheme.Colors themeColor){
        CellStyle newCellStyle = cellStyle.clone();

        Fill fill = (Fill) cellStyle.getProperty(CellStyle.Property.FILL);
        if(fill != null){
            if(fill instanceof PatternFill)
            {
                ZSColor fgCcolor = ((PatternFill) fill).getFgColor();
                ZSColor bgCcolor = ((PatternFill) fill).getBgColor();
                if(fgCcolor instanceof ZSThemeColor)
                {
                    String tint = fgCcolor.getColorTintToWrite();
                    String theme = fgCcolor.getThemeColorToWrite();
                    if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)) {
                        fgCcolor = ZSColor.getInstance(themeColor, tint != null ? Double.parseDouble(tint) : 0.0);
                    }
                }

                if(bgCcolor instanceof ZSThemeColor)
                {
                    String tint = bgCcolor.getColorTintToWrite();
                    String theme = bgCcolor.getThemeColorToWrite();
                    if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)) {
                        bgCcolor = ZSColor.getInstance(themeColor, tint != null ? Double.parseDouble(tint) : 0.0);
                    }
                }
                fill = PatternFill.getInstance(((PatternFill) fill).getPatternType(), fgCcolor, bgCcolor);

            }
            else {
                Map<Double, ZSColor> newMap = new HashMap<>();
                for (Double position : ((GradientFill)fill).getGradientColors().keySet()) {
                    ZSColor color = ((GradientFill)fill).getGradientColors().get(position);
                    if (color instanceof ZSThemeColor) {
                        String tint = color.getColorTintToWrite();
                        String theme = color.getThemeColorToWrite();
                        if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)) {
                            color = ZSColor.getInstance(themeColor, tint != null ? Double.parseDouble(tint) : 0.0);
                        }
                    }
                    newMap.put(position, color);
                }

                fill = GradientFill.getInstance(((GradientFill) fill).getDegree(), ((GradientFill) fill).getGradientType(), ((GradientFill) fill).getLeft(), ((GradientFill) fill).getRight(), ((GradientFill) fill).getTop(), ((GradientFill) fill).getBottom(), newMap);
            }

            newCellStyle.setProperty(CellStyle.Property.FILL, fill);
        }
        Object colorObj = newCellStyle.getProperty(TextStyle.Property.COLOR);

        if(colorObj != null){
            String tint = ((ZSColor)colorObj).getColorTintToWrite();
            String theme = ((ZSColor)colorObj).getThemeColorToWrite();
            if(theme != null && !ZSColorScheme.Colors.BACKGROUND1.toString().equals(theme) && !ZSColorScheme.Colors.TEXT1.toString().equals(theme)){
                newCellStyle.setProperty(TextStyle.Property.COLOR,ZSColor.getInstance(themeColor,tint!= null ? Double.parseDouble(tint) : 0.0));
            }
        }
        BorderProperties props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERLEFT);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    newCellStyle.setProperty(CellStyle.Property.BORDERLEFT, BorderProperties.getInstance(props.getType(),ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0),props.getSize()));
                }
            }
        }
        props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERRIGHT);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    newCellStyle.setProperty(CellStyle.Property.BORDERRIGHT, BorderProperties.getInstance(props.getType(),ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0),props.getSize()));
                }
            }
        }
        props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERTOP);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    newCellStyle.setProperty(CellStyle.Property.BORDERTOP, BorderProperties.getInstance(props.getType(),ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0),props.getSize()));
                }
            }
        }
        props = (BorderProperties) CellStyle.getProperty(newCellStyle.getCellStyleProperties(),CellStyle.Property.BORDERBOTTOM);
        if(props != null) {
            ZSColor color = props.getColor();
            if (color != null) {
                if (!ZSColorScheme.Colors.BACKGROUND1.toString().equals(color.getThemeColorToWrite()) && !ZSColorScheme.Colors.TEXT1.toString().equals(color.getThemeColorToWrite())) {
                    newCellStyle.setProperty(CellStyle.Property.BORDERBOTTOM, BorderProperties.getInstance(props.getType(),ZSColor.getInstance(themeColor,color.getColorTintToWrite() != null ? Double.parseDouble( color.getColorTintToWrite()) : 0.0),props.getSize()));
                }
            }
        }
        return newCellStyle;
    }
    public static CellStyle getSlicerStyle(int styleInd, int accent, Slicer.SlicerStyle styleKey){
        ZSColorScheme.Colors themeColor = ZSColorScheme.Colors.ACCENT1;
        switch(accent)
        {
            case 2:
                themeColor = ZSColorScheme.Colors.ACCENT2;
                break;
            case 3:
                themeColor = ZSColorScheme.Colors.ACCENT3;
                break;
            case 4:
                themeColor = ZSColorScheme.Colors.ACCENT4;
                break;
            case 5:
                themeColor = ZSColorScheme.Colors.ACCENT5;
                break;
            case 6:
                themeColor = ZSColorScheme.Colors.ACCENT6;
                break;
        }
        return  replaceAccentInStyle(SLICER_STYLE_LIST.get(styleInd).get(styleKey),themeColor);

    }

    public static Shadow getSlicerShadow(int styleIndex, ZSColorScheme.Colors accent)
    {
        switch(styleIndex)
        {
            case 0:
                Shadow shadow = new Shadow(ZSColor.getInstance(accent, 0));
                shadow.setTransparency(90);
                shadow.setBlur(1);
                shadow.setAngle(72);
                shadow.setDistance(3.16);
                return shadow;
            case 1:
                shadow = new Shadow(ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0));
                shadow.setTransparency(90);
                shadow.setBlur(1);
                shadow.setAngle(72);
                shadow.setDistance(3.16);
                return shadow;
            case 3:
                shadow = new Shadow(ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0));
                shadow.setTransparency(90);
                shadow.setBlur(1);
                shadow.setAngle(72);
                shadow.setDistance(3.16);
                return shadow;
            case 4:
                shadow = new Shadow(ZSColor.getInstance(ZSColorScheme.Colors.TEXT1, 0));
                shadow.setTransparency(90);
                shadow.setBlur(1);
                shadow.setAngle(72);
                shadow.setDistance(3.16);
                return shadow;
            case 7:
                shadow = new Shadow(ZSColor.getInstance(accent,0.82));
                shadow.setTransparency(0);
                shadow.setBlur(0);
                shadow.setInner(true);
                shadow.setSpread(6);
                return shadow;
            default:
                return null;
        }
    }

}
