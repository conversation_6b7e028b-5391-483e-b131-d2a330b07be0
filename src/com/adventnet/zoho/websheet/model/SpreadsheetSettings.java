package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ext.ParsedDate;
import com.adventnet.zoho.websheet.model.ext.functions.Rept;
import com.adventnet.zoho.websheet.model.style.datastyle.*;
import com.adventnet.zoho.websheet.model.util.*;

import java.text.*;
import java.util.*;
import java.util.logging.Logger;

public class SpreadsheetSettings {

    public static final Logger LOGGER = Logger.getLogger(SpreadsheetSettings.class.getName());
    public static SpreadsheetSettings defaultSpreadsheetSettings = new SpreadsheetSettings(EngineConstants.DEFAULT_LOCALE);

    private static final Map<String, SpreadsheetSettings> LOCALE_SPREADSHEETSETTINGS_CACHE = new HashMap<>();
    private static final Map<String, SpreadsheetSettings> PROPS_SPREADSHEETSETTINGS_CACHE = new HashMap<>();

    public enum DateSeparator {
        DOT('.'),
        <PERSON><PERSON><PERSON>('/'),
        HYPHEN('-');
        private final char separator;
        DateSeparator(char in)
        {
            this.separator = in;
        }

        public char getSeparator()
        {
            return separator;
        }

        private static DateSeparator getDateSeparator(char in)
        {
            switch(in)
            {
                case '.':
                    return DOT;
                case '/':
                    return SLASH;
                case '-':
                    return HYPHEN;
            }

            throw new IllegalArgumentException("Invalid char for separator");//No I18N
        }
    }

    public enum DecimalSeparator
    {
        DOT('.'),
        COMMA(',');

        private final char separator;
        DecimalSeparator(char in)
        {
            this.separator = in;
        }

        public char getSeparator()
        {
            return separator;
        }

        public static DecimalSeparator getDecimalSeparator(char in)
        {
            for(DecimalSeparator decimalSeparator : DecimalSeparator.values())
            {
                if(decimalSeparator.getSeparator() == in)
                {
                    return decimalSeparator;
                }
            }
            return DOT;
        }
    }

    public enum ThousandSeparator
    {
        DOT('.'),
        COMMA(','),
        SPACE(' '),
        APOSTROPHE('\'');

        private final char separator;
        private static final char NON_BREAKING_SPACE = '\u00A0';
        private static final char NARROW_NON_BREAKING_SPACE = '\u202F';
        ThousandSeparator(char in)
        {
            this.separator = in;
        }

        public char getSeparator()
        {
            return separator;
        }

        public static ThousandSeparator getThousandSeparator(char in)
        {
            // SPACE WILL BE USED FOR NON-BREAKING SPACE AND NARROW NON-BREAKING SPACE.
            if(in == NON_BREAKING_SPACE || in == NARROW_NON_BREAKING_SPACE)
            {
                return SPACE;
            }
            
            for(ThousandSeparator thousandSeparator : ThousandSeparator.values())
            {
                if(thousandSeparator.getSeparator() == in)
                {
                    return thousandSeparator;
                }
            }
            
            return COMMA;
        }
    }

    public enum DisplayDirection
    {
        LEFT_TO_RIGHT(1, "lr-tb"), // No I18N
        RIGHT_TO_LEFT(2, "rl-tb"); // No I18N

        private final int id;
        private final String writingMode;
        DisplayDirection(int id, String writingMode)
        {
            this.id = id;
            this.writingMode = writingMode;
        }

        public int getId()
        {
            return this.id;
        }

        public static DisplayDirection getDisplayDirection(int id)
        {
            for(DisplayDirection direction : DisplayDirection.values())
            {
                if(direction.id == id)
                {
                    return direction;
                }
            }
            return LEFT_TO_RIGHT;
        }

        public String getWritingMode()
        {
            return this.writingMode;
        }

        public String getName()
        {
            if(this.equals(LEFT_TO_RIGHT))
            {
                return "Left to Right"; // No I18N
            }
            return "Right to Left"; // No I18N
        }

        @Override
        public String toString()
        {
            return String.valueOf(id);
        }
    }

    public enum NumberGroupingType {
        INDIAN(1),
        GLOBAL(2);

        private int id;
        NumberGroupingType(int id) {
            this.id = id;
        }

        public int getId() {
            return id;
        }

        public static NumberGroupingType valueOf(int id) {
            return id == 1 ? INDIAN : GLOBAL;
        }
    }

    private static String getLocaleLanguage(String language, String countryName)
    {
        List<String> mappedLanguages = new ArrayList<>();
        for (Locale loc : Locale.getAvailableLocales())
        {
            if (Utility.masknull(loc.getCountry(), "").equalsIgnoreCase(countryName))
            {
                // If there is a locale available in java with the given language and country, use the same language and country.
                if(Utility.masknull(loc.getLanguage(), "").equalsIgnoreCase(language))
                {
                    return language;
                }
                //////////////////
                mappedLanguages.add(loc.getLanguage());
            }
        }

        if (mappedLanguages.isEmpty())
        {
            return "en";//NO I18N
        }
        if (mappedLanguages.contains("en"))
        {
            return "en";//NO I18N
        } else {
            return mappedLanguages.get(0);
        }
    }

    private static Locale getLocaleCorrected(Locale inLocale)
    {
        if(inLocale == null)
        {
            // SHOULD WE THROW ERROR HERE ?
            inLocale = EngineConstants.DEFAULT_LOCALE;
        }
        else
        {
            if(inLocale.getCountry() == null || inLocale.getCountry().isEmpty())
            {
                inLocale = LocaleUtil.getLocale(inLocale.getLanguage(), "US");//No I18N
            }
            // SP is deprecated country code of SERBIA. https://cygwin.com/cygwin-ug-net/setup-locale.html
            else if(inLocale.getCountry().equals("SP"))   //No I18N
            {
                inLocale = LocaleUtil.getLocale(inLocale.getLanguage(), "RS");//No I18N
            }
            // EU is used to denote Europe. Not yet supported by Java. Hence using Germany as country.
            else if(inLocale.getCountry().equals("EU"))   //No I18N
            {
                inLocale = LocaleUtil.getLocale(inLocale.getLanguage(), "DE");//No I18N
            }

            String inLanguage = inLocale.getLanguage();
            String inCountry = inLocale.getCountry();
            String correctLanguage = getLocaleLanguage(inLanguage, inCountry);
            if(!correctLanguage.equals(inLanguage))
            {
                inLocale = LocaleUtil.getLocale(correctLanguage, inCountry);
            }
        }

        return inLocale;
    }

    public static SpreadsheetSettings getInstance(Locale inLocale)
    {
        //inLocale = getLocaleCorrected(inLocale);

        String cacheKey = inLocale.toString();
        SpreadsheetSettings cached = LOCALE_SPREADSHEETSETTINGS_CACHE.get(cacheKey);
        if(cached == null)
        {
            cached = new SpreadsheetSettings(inLocale);
            LOCALE_SPREADSHEETSETTINGS_CACHE.put(cacheKey, cached);
        }
        return cached;
    }

    public static SpreadsheetSettings getInstance(Locale inLocale, Locale inCurrencyLocale, char inDecimalSeparator, char inThousandsSeparator, DateUtil.DateFormatType inDateInputFormat, DisplayDirection inDisplayDirection, NumberGroupingType groupingType, boolean isAllowSortOnLockedRanges)
    {
        //inLocale = getLocaleCorrected(inLocale);
        inCurrencyLocale = getLocaleCorrected(inCurrencyLocale);

        String cacheKey = inLocale.toString()+inCurrencyLocale.toString()+inDecimalSeparator+inThousandsSeparator+inDateInputFormat.toString()+inDisplayDirection.toString()+groupingType.toString()+isAllowSortOnLockedRanges;
        SpreadsheetSettings cached = PROPS_SPREADSHEETSETTINGS_CACHE.get(cacheKey);
        if(cached == null)
        {
            cached = new SpreadsheetSettings(inLocale, inCurrencyLocale, inDecimalSeparator, inThousandsSeparator, inDateInputFormat, inDisplayDirection, groupingType, isAllowSortOnLockedRanges);
            PROPS_SPREADSHEETSETTINGS_CACHE.put(cacheKey, cached);
        }
        return cached;
    }

    public static SpreadsheetSettings getInstance(Locale locale, DateUtil.DateFormatType dateInputFormat)
    {
        SpreadsheetSettings temp = getInstance(locale);
        return getInstance(temp.getLocale(), temp.getCurrencyLocale(), temp.getDecimalSeparator(), temp.getThousandSeparator(), dateInputFormat, temp.getDisplayDirection(), temp.getNumberGroupingType(), temp.isAllowSortOnLockedRanges());
    }

    public static SpreadsheetSettings getInstance(Locale locale, SpreadsheetSettings.DisplayDirection displayDirection)
    {
        SpreadsheetSettings temp = getInstance(locale);
        return getInstance(temp.getLocale(), temp.getCurrencyLocale(), temp.getDecimalSeparator(), temp.getThousandSeparator(), temp.getDateInputFormat(), displayDirection, temp.getNumberGroupingType(), temp.isAllowSortOnLockedRanges());
    }

    private class DisplayFormats
    {
        private ZSSimpleDateFormat dateDisplayFormat;
        private ZSSimpleDateFormat dateTimeDisplayFormat;
        private ZSSimpleDateFormat dateTimeWithMSDisplayFormat;

        private ZSPattern durationFormat;
        private ZSPattern durationFormatWithMS;

        private DecimalFormat numberFormat;
        private DecimalFormat percentageFormat;
        private DecimalFormat scientificFormat;

        private ZSSimpleDateFormat getDateFormat(Cell.Type type, boolean hasMilliSecs)
        {
            if(type == Cell.Type.DATE)
            {
                return this.dateDisplayFormat;
            }
            else // DATETIME
            {
                if(hasMilliSecs)
                {
                    return this.dateTimeDisplayFormat;
                }
                else
                {
                    return this.dateTimeWithMSDisplayFormat;
                }
            }
        }

        private void setDateFormat(Cell.Type type, boolean hasMilliSecs, ZSSimpleDateFormat format)
        {
            if(type == Cell.Type.DATE)
            {
                this.dateDisplayFormat = format;
            }
            else // DATETIME
            {
                if(hasMilliSecs)
                {
                    this.dateTimeDisplayFormat = format;
                }
                else
                {
                    this.dateTimeWithMSDisplayFormat = format;
                }
            }
        }

        public DecimalFormat getNumberFormat(Cell.Type type){
            switch (type){
                case FLOAT:
                    return this.numberFormat;
                case PERCENTAGE:
                    return this.percentageFormat;
                case SCIENTIFIC:
                    return this.scientificFormat;
                default:
                    throw new IllegalArgumentException("Invalid type for Numberformat");//No I18N
            }
        }

        public void setNumberFormat(Cell.Type type, DecimalFormat format){
            switch (type){
                case FLOAT:
                    this.numberFormat = format;
                    break;
                case PERCENTAGE:
                    this.percentageFormat = format;
                    break;
                case SCIENTIFIC:
                    this.scientificFormat = format;
                    break;
                default:
                    throw new IllegalArgumentException("Invalid type for Numberformat");//No I18N
            }
        }
        private ZSPattern getDurationFormat(boolean hasMilliSeconds)
        {
            return hasMilliSeconds ? this.durationFormatWithMS : this.durationFormat;
        }

        private void setDurationFormat(boolean hasMilliSeconds, ZSPattern inDurationFormat)
        {
            if(hasMilliSeconds)
            {
                this.durationFormatWithMS = inDurationFormat;
            }
            else
            {
                this.durationFormat = inDurationFormat;
            }
        }
    }

    // Primary Attributes
    private final Locale locale;
    private final DateUtil.DateFormatType dateInputFormat;
    //private DateDisplayFormat dateDisplayFormat;
    private final DecimalSeparator decimalSeparator;
    private final ThousandSeparator thousandSeparator;
    private final Locale currencyLocale;
    private final DisplayDirection displayDirection;
    private final NumberGroupingType numberGroupingType;
    private final boolean isAllowSortOnLockedRanges;
    private boolean allowSpaceCharsForGroupingSeparator;

    // Secondary Attributes. Mostly cached to avoid creating them multiple times
    private final ThreadLocal<DisplayFormats> displayFormats = new ThreadLocal<>();
    private SpreadsheetSettings(Locale inLocale){
        DecimalFormatSymbols dfs = new DecimalFormatSymbols(inLocale);
        this.locale = inLocale;
        this.currencyLocale = inLocale;
        this.decimalSeparator = DecimalSeparator.getDecimalSeparator(dfs.getDecimalSeparator());
        this.thousandSeparator = ThousandSeparator.getThousandSeparator(dfs.getGroupingSeparator());
        this.dateInputFormat = LocaleUtil.getDateFormatType(inLocale);
        //this.dateDisplayFormat = new DateDisplayFormat(inLocale);
        this.displayDirection = DocumentUtils.isRTLLanguage(inLocale.getLanguage()) ? DisplayDirection.RIGHT_TO_LEFT : DisplayDirection.LEFT_TO_RIGHT;
        this.numberGroupingType = "IN".equals(inLocale.getCountry()) ? NumberGroupingType.INDIAN : NumberGroupingType.GLOBAL; //No I18N
        this.isAllowSortOnLockedRanges = false;
    }

    private SpreadsheetSettings(Locale inLocale, Locale inCurrencyLocale, char inDecimalSeparator, char inThousandsSeparator, DateUtil.DateFormatType inDateInputFormat, DisplayDirection inDisplayDirection, NumberGroupingType groupingType, boolean isAllowSortOnLockedRanges)
    {
        this.locale = inLocale;
        this.currencyLocale = inCurrencyLocale;
        this.decimalSeparator = DecimalSeparator.getDecimalSeparator(inDecimalSeparator);
        this.thousandSeparator = ThousandSeparator.getThousandSeparator(inThousandsSeparator);
        this.dateInputFormat = inDateInputFormat;
        //this.dateDisplayFormat = new DateDisplayFormat(inLocale);
        this.displayDirection = inDisplayDirection;
        this.numberGroupingType = groupingType;
        this.isAllowSortOnLockedRanges = isAllowSortOnLockedRanges;
    }

    public DateUtil.DateFormatType getDateInputFormat()
    {
        return this.dateInputFormat;
    }

    public char getDecimalSeparator()
    {
        return this.decimalSeparator.getSeparator();//return this.decimalSeparator.separator;
    }

    public char getThousandSeparator()
    {
        return this.thousandSeparator.getSeparator();//return this.thousandSeparator.separator;
    }

    public Locale getCurrencyLocale()
    {
        return this.currencyLocale;
    }

    public DisplayDirection getDisplayDirection()
    {
        return this.displayDirection;
    }

    public NumberGroupingType getNumberGroupingType() {
        return this.numberGroupingType;
    }

    public Locale getLocale(){
        if(this.locale == null)
        {
            return EngineConstants.DEFAULT_LOCALE;
        }
        return this.locale;
    }
    public ZSSimpleDateFormat getDateTimeFormat(Cell.Type type, boolean hasMilliSeconds)
    {
        DisplayFormats displayFormats1 = this.displayFormats.get();
        if (displayFormats1 == null) {
            displayFormats1 = new DisplayFormats();
            this.displayFormats.set(displayFormats1);
        }
        ZSSimpleDateFormat f = displayFormats1.getDateFormat(type, hasMilliSeconds);

        if(f == null) {
            Format baseFormat = DateFormat.getDateInstance(DateFormat.SHORT, locale);
            String datePattern = ((SimpleDateFormat)baseFormat).toPattern();

            boolean isDoubleD = datePattern.contains("dd");//No I18N
            boolean isDoubleM = datePattern.contains("MM");//No I18N
            DateSeparator separator = datePattern.contains("-") ? DateSeparator.HYPHEN : datePattern.contains(".") ? DateSeparator.DOT : DateSeparator.SLASH;

            String date = isDoubleD ? "dd" : "d";//No I18N
            String month = isDoubleM ? "MM" : "M";//No I18N
            String year = "yyyy";//No I18N
            switch (this.dateInputFormat)
            {
                case DMY:
                    datePattern = date + separator.separator + month + separator.separator + year;
                    break;
                case MDY:
                    datePattern = month + separator.separator + date + separator.separator +year;
                    break;
                case YMD:
                    datePattern = year + separator.separator + month + separator.separator + date;
                    break;
                case YDM:
                    datePattern = year + separator.separator + date + separator.separator + month;
                    break;
                default:
                    datePattern = month + separator.separator + date + separator.separator +year;
                    break;
            }




            if (type == Cell.Type.DATETIME) {
                datePattern += " "+((SimpleDateFormat)DateFormat.getTimeInstance(DateFormat.MEDIUM, locale)).toPattern();

                if (hasMilliSeconds) {
                    String timeStringWithMilli = "ss" + this.getDecimalSeparator() + Rept.rept("S", 3); //No I18N
                    datePattern = datePattern.replace("ss", timeStringWithMilli);
                }
            }

            f = new ZSSimpleDateFormat(datePattern, locale);
            displayFormats1.setDateFormat(type, hasMilliSeconds, f);
        }

        return f;
    }

    public ZSPattern getDurationFormat(boolean hasMilliSeconds) {
        DisplayFormats displayFormats1 = this.displayFormats.get();
        if (displayFormats1 == null) {
            displayFormats1 = new DisplayFormats();
            this.displayFormats.set(displayFormats1);
        }
        ZSPattern pattern = displayFormats1.getDurationFormat(hasMilliSeconds);
        if(pattern == null) {
            List<PatternComponent> components = new ArrayList<>();
            components.add(DefaultPatternUtil.getDefaultTimeFormat(locale, true, ParsedDate.TimeType.DURATION));

            if(hasMilliSeconds) {
                components.add(new ZSMillisecondComponent(3, locale));
            }

            pattern = new ZSPattern(components, Cell.Type.TIME, null, false, false, false);
            displayFormats1.setDurationFormat(hasMilliSeconds, pattern);
        }

        return pattern;
    }

    public DecimalFormat getNumberFormat(Cell.Type type){
        DisplayFormats displayFormats1 = this.displayFormats.get();
        if (displayFormats1 == null) {
            displayFormats1 = new DisplayFormats();
            this.displayFormats.set(displayFormats1);
        }
        DecimalFormat df = displayFormats1.getNumberFormat(type);
        if(df == null){
            DecimalFormatSymbols dfs = new DecimalFormatSymbols(this.locale);
            dfs.setDecimalSeparator(this.getDecimalSeparator());
            dfs.setGroupingSeparator(this.getThousandSeparator());
            if(type == Cell.Type.SCIENTIFIC)
            {
                df = new DecimalFormat("0.##############E00", dfs);//No I18N

            }
            else
            {
//                df = (DecimalFormat) NumberFormat.getNumberInstance(locale);
                df = new DecimalFormat();
                df.setDecimalFormatSymbols(dfs);

                if(type == Cell.Type.PERCENTAGE)
                {
                    df.setPositiveSuffix("%");
                    df.setNegativeSuffix("%");
                }
                df.setGroupingUsed(false);
                df.setMinimumFractionDigits(0);
                df.setMaximumFractionDigits(15);
            }
            displayFormats1.setNumberFormat(type, df);
        }
        return df;
    }

    public boolean isCheckWithSpaceGrouping()
    {
        return this.allowSpaceCharsForGroupingSeparator;
    }

    public boolean isAllowSortOnLockedRanges()
    {
        return isAllowSortOnLockedRanges;
    }


    public JSONObjectWrapper getJSON()
    {
        JSONObjectWrapper localeJSON = new JSONObjectWrapper();
        localeJSON.put(JSONConstants.LOCALE, this.getLocale().toString());
        localeJSON.put(Integer.toString(CommandConstants.LANGUAGE), this.getLocale().getLanguage());
        localeJSON.put(Integer.toString(CommandConstants.COUNTRY), this.getLocale().getCountry());
        localeJSON.put(JSONConstants.THOUSAND_SEP, (int) this.getThousandSeparator());
        localeJSON.put(JSONConstants.DECIMAL_SEP, this.getDecimalSeparator());
        localeJSON.put(JSONConstants.DATE_FORMAT, this.getDateInputFormat().getId());
        localeJSON.put(JSONConstants.NUMBER_GROUPING_TYPE, this.getNumberGroupingType().getId());
        localeJSON.put(JSONConstants.CURRENCY_LOCALE, this.getCurrencyLocale().toString());
//		localeJSON.put(JSONConstants.DATE_DISPLAY_FORMAT, dateDisplayFormat.toPatternString(EngineConstants.DEFAULT_LOCALE));
        localeJSON.put(Integer.toString(CommandConstants.DATE_SEPARATOR), Character.toString((char) StyleActionUtil.getDateSeparator(this.getLocale())));
        localeJSON.put(JSONConstants.SHEET_DIRECTION, this.getDisplayDirection().getId());

        return localeJSON;
    }

    @Override
    public boolean equals(Object o)
    {
        if(this == o)
        {
            return true;
        }
        if(!(o instanceof SpreadsheetSettings))
        {
            return false;
        }
        SpreadsheetSettings that = (SpreadsheetSettings) o;
        return isAllowSortOnLockedRanges == that.isAllowSortOnLockedRanges && allowSpaceCharsForGroupingSeparator == that.allowSpaceCharsForGroupingSeparator && Objects.equals(locale, that.locale) && dateInputFormat == that.dateInputFormat && decimalSeparator == that.decimalSeparator && thousandSeparator == that.thousandSeparator && Objects.equals(currencyLocale, that.currencyLocale) && displayDirection == that.displayDirection && numberGroupingType == that.numberGroupingType && Objects.equals(displayFormats, that.displayFormats);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(locale, dateInputFormat, decimalSeparator, thousandSeparator, currencyLocale, displayDirection, numberGroupingType, isAllowSortOnLockedRanges, allowSpaceCharsForGroupingSeparator, displayFormats);
    }
}
