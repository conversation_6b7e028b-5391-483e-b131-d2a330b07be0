package com.adventnet.zoho.websheet.model.shapes;

import com.adventnet.zoho.websheet.model.writer.XMLWriter;

public class Equation {
	private String name = null;
	private String formula = null;
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getFormula() {
		return formula;
	}
	public void setFormula(String formula) {
		this.formula = formula;
	}
	
	public Equation clone() {
		Equation equation = new Equation();
		equation.setFormula(this.formula);
		equation.setName(this.name);
		return equation;
	}
	
	public String[] getAttributes() {
		String[] attrs = new String[]
				{
						"draw:name", //No I18N
						"draw:formula" //No I18N
				};
		return attrs;
	}
	
	public String[] getValues() {
		String[] values = new String[] {
				getName(),
				getFormula()
		};
		return values;
	}
	public String getEquationXML() {
		return XMLWriter.createStartTagClose("draw:equation", this.getAttributes(), this.getValues(), false);	//No I18N	
	}
}
