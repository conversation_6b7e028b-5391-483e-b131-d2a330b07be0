package com.adventnet.zoho.websheet.model.shapes;

import com.adventnet.zoho.websheet.model.CellReference.ReferenceMode;
import com.adventnet.zoho.websheet.model.Range;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.exception.SheetEngineException;
import com.adventnet.zoho.websheet.model.writer.XMLWriter;

public class CustomShape extends Shape{
	private String endCellAddress = null;
	private Range endCellRange = null;
	private String endX = null;
	private String endY = null;
	private String name = null;
	private String width = null;
	private String height = null;
	private String svgX = null;
	private String svgY = null;	
	private EnhancedGeometry enhancedGeometry = null;
	
	public CustomShape(String id, String zIndex, String styleName, String textStyleName, String endCellAddress, String endX, String endY, String name, String height, String width, String svgX, String svgY) {
		super(id, zIndex, styleName, textStyleName, ShapeType.CUSTOMSHAPE);
		this.endCellAddress = endCellAddress;
		this.endX = endX;
		this.endY = endY;
		this.name = name;
		this.height = height;
		this.width = width;
		this.svgX = svgX;
		this.svgY = svgY;		
	}
	
	public CustomShape(String shapeID, String zIndex, String styleName, String textStyleName) {
		super(shapeID, zIndex, styleName, textStyleName, ShapeType.CUSTOMSHAPE);
	}

	public void initEndCellRange(Workbook workbook) throws SheetEngineException {
		if(endCellAddress != null) {
			this.endCellRange = new Range(workbook, endCellAddress, null, ReferenceMode.A1, true);
		}
	}
	
	public String getEndCellAddress() {
		return endCellAddress;
	}

	public void setEndCellAddress(String endCellAddress) {
		this.endCellAddress = endCellAddress;
	}

	public String getEndX() {
		return endX;
	}

	public void setEndX(String endX) {
		this.endX = endX;
	}

	public String getEndY() {
		return endY;
	}

	public void setEndY(String endY) {
		this.endY = endY;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getSvgX() {
		return svgX;
	}

	public void setSvgX(String svgX) {
		this.svgX = svgX;
	}

	public String getSvgY() {
		return svgY;
	}

	public void setSvgY(String svgY) {
		this.svgY = svgY;
	}
	
	public Shape clone() {
		CustomShape customShape = new CustomShape(this.getId(), this.getzIndex(), this.getStyleName(), this.getTextStyleName(), this.endCellAddress, this.endX, this.endY, this.name, this.height, this.width, this.svgX, this.svgY);
		customShape.setEnhancedGeometry(enhancedGeometry.clone());
		return customShape;
	}


	public String[] getAttributes() {
		String[] attrs = new String[]
				{
						"draw:id", //No I18N
						"table:end-cell-address", //No I18N
						"table:end-x", //No I18N
						"table:end-y", //No I18N
						"draw:z-index", //No I18N
						"draw:name", //No I18N
						"draw:style-name", //No I18N
						"draw:text-style-name", //No I18N
						"svg:width", //No I18N
						"svg:height", //No I18N
						"svg:x", //No I18N
						"svg:y" //No I18N
				};
		return attrs;
	}
	
	public String[] getValues() {
		String[] values = new String[] {
				(this.getId()),
				(getEndCellRange() == null) ? null : getEndCellRange().getCellRangeAddress(), 
				getEndX(), 
				getEndY(),
				getzIndex(),
				getName(),
				getStyleName(),
				getTextStyleName(),
				getWidth(),
				getHeight(),
				getSvgX(),
				getSvgY()
		};
		return values;
	}

	public EnhancedGeometry getEnhancedGeometry() {
		return enhancedGeometry;
	}

	public void setEnhancedGeometry(EnhancedGeometry enhancedGeometry) {
		this.enhancedGeometry = enhancedGeometry;
	}	
	
	public String getTagName() {
		return "draw:custom-shape"; //No I18N
	}

	@Override
	public String getShapeXml() {
		StringBuilder buff = new StringBuilder();		
		buff.append(XMLWriter.createStartTagOpen("draw:custom-shape", this.getAttributes(), this.getValues(), false));
		buff.append(this.getTextInShape());
		buff.append(this.getEnhancedGeometry() == null ? "" : this.getEnhancedGeometry().getXml());
		buff.append(XMLWriter.createEndTag("draw:custom-shape"));
		
		return buff.toString();
	}

	public Range getEndCellRange() {
		return endCellRange;
	}
}