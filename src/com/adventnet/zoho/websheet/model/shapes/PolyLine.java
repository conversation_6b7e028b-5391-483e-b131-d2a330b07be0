package com.adventnet.zoho.websheet.model.shapes;

import com.adventnet.zoho.websheet.model.writer.XMLWriter;

public class PolyLine extends Shape{
	private String transform = null;
	private String viewBox = null;
	private String height = null;
	private String width = null;
	private String points = null;
	
	
	public PolyLine(String id, String zIndex, String styleName, String textStyleName, String transform, String viewBox, String height, String width, String points) {
		super(id, zIndex, styleName, textStyleName, ShapeType.POLYLINE);
		this.transform = transform;
		this.viewBox = viewBox;
		this.height = height;
		this.width = width;
		this.points = points;
	}
	
	public PolyLine(String shapeID, String zIndex, String styleName, String textStyleName) {
		super(shapeID, zIndex, styleName, textStyleName, ShapeType.POLYLINE);
	}

	public String getTransform() {
		return transform;
	}
	public void setTransform(String transform) {
		this.transform = transform;
	}
	public String getViewBox() {
		return viewBox;
	}
	public void setViewBox(String viewBox) {
		this.viewBox = viewBox;
	}
	public String getHeight() {
		return height;
	}
	public void setHeight(String height) {
		this.height = height;
	}
	public String getWidth() {
		return width;
	}
	public void setWidth(String width) {
		this.width = width;
	}
	public String getPoints() {
		return points;
	}
	public void setPoints(String points) {
		this.points = points;
	}
	
	public Shape clone() {
		Shape polyLine = new PolyLine(this.getId(), this.getzIndex(), this.getStyleName(), this.getTextStyleName(), this.transform, this.viewBox, this.height, this.width, this.points);
		return polyLine;		
	}
	
	public String[] getAttributes() {
		String[] attrs = new String[]
				{
						"draw:id", //No I18N
						"draw:style-name", //No I18N
						"draw:text-style-name", //No I18N
						"draw:z-index", //No I18N
						"draw:transform", //No I18N
						"svg:height", //No I18N
						"svg:width", //No I18N
						"svg:viewBox", //No I18N
						"draw:points" //No I18N
				};
		return attrs;
	}
	
	public String[] getValues() {
		String[] values = new String[] {
				this.getId(),
				this.getStyleName(),
				this.getTextStyleName(),
				this.getzIndex(),
				this.getTransform(),
				this.getHeight(),
				this.getWidth(),
				this.getViewBox(),
				this.getPoints()
		};
		return values;
	}
	
	public String getTagName() {
		return "draw:polyline"; //No I18N
	}

	@Override
	public String getShapeXml() {
		StringBuilder buff = new StringBuilder();
		
		buff.append(XMLWriter.createStartTagOpen("draw:polyline", this.getAttributes(), this.getValues(), false));
		buff.append(this.getTextInShape());
		buff.append(XMLWriter.createEndTag("draw:polyline"));		
		return buff.toString();
	}
}
