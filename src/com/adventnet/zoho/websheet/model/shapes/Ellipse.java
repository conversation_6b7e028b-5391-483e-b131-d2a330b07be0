package com.adventnet.zoho.websheet.model.shapes;

import com.adventnet.zoho.websheet.model.writer.XMLWriter;

public class Ellipse extends Shape{
	
	private String height = null;
	private String width = null;
	private String svgX = null;
	private String svgY = null;	
	private String endAngle = null;
	private String startAngle = null;
	private String kind = null;
	
	public Ellipse(String id, String zIndex, String styleName, String textStyleName, String height, String width, String x, String y, String startAngle, String endAngle, String kind) {
		super(id, zIndex, styleName, textStyleName, ShapeType.ELLIPSE);
		this.height = height;
		this.width = width;
		this.svgX = x;
		this.svgY = y;
		this.startAngle = startAngle;
		this.endAngle = endAngle;
		this.kind = kind;
	}
	
	public Ellipse(String shapeID, String zIndex, String styleName, String textStyleName) {
		super(shapeID, zIndex, styleName, textStyleName, ShapeType.ELLIPSE);
	}

	public String[] getAttributes() {
		String[] attrs = new String[]
				{
						"draw:id", //No I18N
						"draw:style-name", //No I18N
						"draw:text-style-name", //No I18N
						"draw:z-index", //No I18N
						"svg:width", //No I18N
						"svg:height", //No I18N
						"svg:x", //No I18N		
						"svg:y", //No I18N
						"draw:kind", //No I18N
						"draw:start-angle", //No I18N
						"draw:end-angle" //No I18N
				};
		return attrs;
	}
	
	public String[] getValues() {
		String[] values = new String[] {
				this.getId(),
				this.getStyleName(),
				this.getTextStyleName(),
				this.getzIndex(),
				this.getWidth(),
				this.getHeight(),
				this.getX(),
				this.getY(),
				this.getKind(),
				this.getStartAngle(),
				this.getEndAngle()
		};
		return values;
	}
	
	public String getTagName() {
		return "draw:ellipse"; //No I18N
	}

	public String getHeight() {
		return height;
	}

	public void setHeight(String height) {
		this.height = height;
	}

	public String getWidth() {
		return width;
	}

	public void setWidth(String width) {
		this.width = width;
	}

	public String getX() {
		return svgX;
	}

	public void setX(String svgX) {
		this.svgX = svgX;
	}

	public String getY() {
		return svgY;
	}

	public void setY(String svgY) {
		this.svgY = svgY;
	}

	public String getEndAngle() {
		return endAngle;
	}

	public void setEndAngle(String endAngle) {
		this.endAngle = endAngle;
	}

	public String getStartAngle() {
		return startAngle;
	}

	public void setStartAngle(String startAngle) {
		this.startAngle = startAngle;
	}

	public String getKind() {
		return kind;
	}

	public void setKind(String kind) {
		this.kind = kind;
	}
	
	public Shape clone() {
		Shape ellipse = new Ellipse(this.getId(), this.getzIndex(), this.getStyleName(), this.getTextStyleName(), this.height, this.width, this.svgX, this.svgY, this.startAngle, this.endAngle, this.kind);
		return ellipse;
	}
	
	public String getShapeXml() {
		StringBuilder buff = new StringBuilder();
		buff.append(XMLWriter.createStartTagOpen("draw:ellipse", this.getAttributes(), this.getValues(), false));
		buff.append(this.getTextInShape());
		buff.append(XMLWriter.createEndTag("draw:ellipse"));		
		return buff.toString();
	}

}
