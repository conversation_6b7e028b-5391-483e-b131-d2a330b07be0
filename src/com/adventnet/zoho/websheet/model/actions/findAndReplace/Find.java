// $Id$
package com.adventnet.zoho.websheet.model.actions.findAndReplace;

import com.adventnet.zoho.websheet.model.*;
import com.adventnet.zoho.websheet.model.util.ColumnUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.RowUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Changes:
 * 1. search in Annotation
 * 2. search in values of formula-cell, right now we only search in formula
 * 3. search in ranges
 * 4. search in array-formula-cells, right now we only search in root-array-formula-cell
 * 5. bug fix - replace in parent-array-formula-cell makes it non array
 */
public class Find {

    private FindInputBean findInputBean;

    public Find(FindInputBean findInputBean) {
        this.findInputBean = findInputBean;
    }

    private boolean isFindTextEqualsString(String string) {
        string = replaceNonBreakingSpaceCharWithSpaceChar(string);
        if(findInputBean.isMatchCase()) {
            return findInputBean.getSearchText().equals(string);
        } else {
            return findInputBean.getSearchText().equalsIgnoreCase(string);
        }
    }

    private boolean isFindTextInString(String string) {
        string = replaceNonBreakingSpaceCharWithSpaceChar(string);
        if(findInputBean.isMatchCase()) {
            return string.contains(findInputBean.getSearchText());
        } else {
            return string.toLowerCase().contains(findInputBean.getSearchText().toLowerCase());
        }
    }

    private boolean isFindRegularExpressionInString(String string) {
        throw new UnsupportedOperationException();
    }

    private void findContent(FindResponseCellBean resultCell) {
        String valueString = resultCell.getCell().getCell().getValue().getValueString(resultCell.getCell().getCell().getSpreadsheetSettings());
        if(valueString != null && valueString.length() != 0) {
            boolean isTextFound = false;
            if(findInputBean.isMatchEntireCellContent()) {
                isTextFound = isFindTextEqualsString(valueString);
            } else {
                isTextFound = isFindTextInString(valueString);
            }
            if(isTextFound) {
                resultCell.setFoundInContent(true);
            }
        }
    }

    private void findFormula(FindResponseCellBean resultCell) {
        boolean isArrayCell = ((CellImpl)resultCell.getCell().getCell()).isArrayCell();
        if(resultCell.getCell().getCell().isFormula() || isArrayCell) {
            String formula = resultCell.getCell().getCell().getLocalizedFormula();
            if(formula != null && formula.length() != 0) {
                if(((CellImpl)resultCell.getCell().getCell()).isArrayCell()) {
                    if(formula.startsWith("{") && formula.endsWith("}")) {
                        formula.substring(1, formula.length() - 1); //got read of '{' and '}' in '{=A1}'
                    }
                }
                boolean isSearchTextFoundInFormula = false;
                if(findInputBean.isMatchEntireCellContent()) {
                    isSearchTextFoundInFormula = isFindTextEqualsString(formula);
                } else {
                    isSearchTextFoundInFormula = isFindTextInString(formula);
                }
                if(isSearchTextFoundInFormula) {
                    resultCell.setFoundInFormula(true);
                }
            }
        }
    }

    private void findAnnotation(FindResponseCellBean resultCell) {
        Annotation annotation = resultCell.getCell().getCell().getAnnotation();
        if(annotation != null) {
            String content = annotation.getContent();
            if(content != null && content.length() != 0) {
                if (isFindTextInString(content)) {
                    resultCell.setFoundInAnnotation(true);
                }
            }
        }
    }

    /**
     * 1. for formula cells, replace will not happen for value
     * 2. for arrayFormula-cells, replace will happen only for root-array-cell
     * 3. replace should not happen for locked cells
     * 4. hidden or filtered cells shouldn't be found
     * @param cell
     * @return
     */
    private FindResponseCellBean find(ReadOnlyCell cell) {

        //to-do: why cell is reaching null here, have to take care of this
        if(cell.getCell() == null) {
            return null;
        }

        FindResponseCellBean resultCell = new FindResponseCellBean(cell);

        if(findInputBean.isFindInContent()) {
            findContent(resultCell);
        }

        if(findInputBean.isFindInFormula()) {
            findFormula(resultCell);
        }

        if(findInputBean.isFindInAnnotations()) {
            findAnnotation(resultCell);
        }

        if(resultCell.isFoundInContent() || resultCell.isFoundInFormula()  || resultCell.isFoundInAnnotation() ) {
            return resultCell;
        }

        return null;
    }

    public List<FindResponseCellBean> find() {

        List<FindResponseCellBean> result = new ArrayList<>();
        if(this.findInputBean.getDataRanges().isEmpty()) {
            return result;
        }

        int startRangeIndex = this.findInputBean.getStartRangeIndex();
        int startRowIndex = this.findInputBean.getStartRowIndex();
        int startColIndex = this.findInputBean.getStartColIndex();

        CyclicIterator cyclicIterator = new CyclicIterator(this.findInputBean.getDataRanges(), startRangeIndex, startRowIndex, startColIndex, !this.findInputBean.isDirectionUp());
        if(this.findInputBean.isStartFromNextCell()) {
            cyclicIterator.moveBy(1);
            cyclicIterator.reset();
        }

        boolean anyMatchInRow = false;

        while (!cyclicIterator.isCompleted()){
            DataRange dataRange = cyclicIterator.getDataRange();

            int rowIndex = cyclicIterator.getRowIndex();

            Sheet sheet = findInputBean.getActiveCell().getSheet().getWorkbook().getSheetByAssociatedName(dataRange.getAssociatedSheetName());
            Row rRow = sheet.getReadOnlyRowFromShell(rowIndex).getRow();

            if(rRow == null) {
                //todo - this can be optimized
                cyclicIterator.moveToEndOfLastRowOfRepetitions(1);
                cyclicIterator.moveBy(1);
                continue;
            }

            if(!findInputBean.isFindInHiddenRows() && !RowUtil.isVisibleRow(rRow)) {
                int repeatedRows = getRepeatedCount(rRow.getRowIndex(), rRow.getRowsRepeated(), rowIndex, !findInputBean.isDirectionUp());
                cyclicIterator.moveToEndOfLastRowOfRepetitions(repeatedRows);
                cyclicIterator.moveBy(1);
                continue;
            }

            int colIndex = cyclicIterator.getColIndex();

            ReadOnlyColumnHeader readOnlyColumnHeader = sheet.getReadOnlyColumnHeader(colIndex);
            if(!findInputBean.isFindInHiddenColumns() && ColumnUtil.isHiddenColumn(readOnlyColumnHeader.getColumnHeader())) {
                if(readOnlyColumnHeader.getColumnHeader() != null) {
                    int repeatedCols = getRepeatedCount(readOnlyColumnHeader.getColumnHeader().getColumn().getColumnIndex(), readOnlyColumnHeader.getColumnHeader().getColsRepeated(), colIndex, !findInputBean.isDirectionUp());
                    cyclicIterator.moveToLastColOfRepetitions(repeatedCols);
                }
                cyclicIterator.moveBy(1);
                continue;
            }

            if(cyclicIterator.isStartOfRow()) {
                anyMatchInRow = false;
            }

            ReadOnlyCell readOnlyCell = sheet.getReadOnlyCellFromShell(rowIndex, colIndex);

            if(readOnlyCell.getCell() == null) {
                int cellNum = rRow.getCellNum();
                if(cellNum <= colIndex) {
                    //the above condition should be always true, if readOnlyCell.getCell() is null
                    int colsRepeated = dataRange.getEndColIndex() - (cellNum - 1);
                    int repeatedCols = getRepeatedCount(cellNum, colsRepeated, colIndex, !findInputBean.isDirectionUp());
                    cyclicIterator.moveToLastColOfRepetitions(repeatedCols);
                }
            } else {
                FindResponseCellBean findResponseCellBeans = find(readOnlyCell);
                if (findResponseCellBeans != null) {
                    anyMatchInRow = true;
                    result.add(findResponseCellBeans);
                } else {
                    int repeatedCols = getRepeatedCount(readOnlyCell.getCell().getColumnIndex(), readOnlyCell.getCell().getColsRepeated(), colIndex, !findInputBean.isDirectionUp());
                    cyclicIterator.moveToLastColOfRepetitions(repeatedCols);
                }
            }

            if (cyclicIterator.isEndOfRow() && !anyMatchInRow && cyclicIterator.isAnyOneRowIteratedFromStartToFinish()) {
                int rowsRepeated = getRepeatedCount(rRow.getRowIndex(), rRow.getRowsRepeated(), rowIndex,!findInputBean.isDirectionUp());
                if(rowsRepeated > 1) {
                    cyclicIterator.moveToEndOfLastRowOfRepetitions(rowsRepeated);
                }
            }

            cyclicIterator.moveBy(1);

            if(!findInputBean.isFindAll() && !result.isEmpty()) {
                break;
            }
        }

        return result;
    }

    public List<FindResponseCellBean> findInActiveCell() {
        FindResponseCellBean findResponseCellBean = find(findInputBean.getActiveCell());
        if(findResponseCellBean == null) {
            return new ArrayList<>();
        }
        return Arrays.asList(findResponseCellBean);
    }

    static String replaceNonBreakingSpaceCharWithSpaceChar(String text) {
        return text.replaceAll(Character.toString((char)160), Character.toString((char)32));
    }

    /**
     * @param originalIndex
     * @param noOfRepeats it can be noOfRowsRepeated, or noOfColumnsRepeated
     * @param repeatIndex in case of cols-repeated it will be colIndex, and in case of rows-repeated it will be rowIndex
     * @param isClockwise
     * @return
     */
    private static int getRepeatedCount(int originalIndex, int noOfRepeats, int repeatIndex, boolean isClockwise) {
        if(originalIndex + noOfRepeats - 1 < repeatIndex) {
            throw new IllegalArgumentException("index is out of parentIndex repetitions" + "[index:"+repeatIndex+",parentIndex:"+originalIndex+",noOfRepeats:"+noOfRepeats+",isClockWise:"+isClockwise+"]:");//No I18N
        } else if(originalIndex > repeatIndex) {
            throw new IllegalArgumentException("index is before parent" + "[,index:"+repeatIndex+",parentIndex:"+originalIndex+",noOfRepeats:"+noOfRepeats+",isClockWise:"+isClockwise+"]:");//No I18N
        }

        if(isClockwise) {
            return originalIndex + noOfRepeats - repeatIndex;
        } else {
            return repeatIndex - originalIndex + 1;
        }
    }

}
