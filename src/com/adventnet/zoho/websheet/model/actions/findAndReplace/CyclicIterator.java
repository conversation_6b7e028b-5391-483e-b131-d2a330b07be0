// $Id$
package com.adventnet.zoho.websheet.model.actions.findAndReplace;

import com.adventnet.zoho.websheet.model.util.DataRange;

import java.util.*;

class CyclicIterator {

    private final List<DataRange> ranges;
    private final int[] startIndexOfRangesInCycle;
    private final int sizeOfCycle;
    private final boolean isClockWise;

    private int completedIteration = 0;
    private int indexInCycle;

    private int rangeIndex;
    private int rowIndex;
    private int colIndex;
    private boolean isAnyOneRowIteratedFromStartToFinish = false;
    private boolean isRowStartedFromStart = false;

    public CyclicIterator(List<DataRange> ranges, int sRangeIndex, int sRowIndex, int sColIndex, boolean isClockWise) {
        int totalRanges = ranges.size();
        if(totalRanges == 0) {
            throw new IllegalArgumentException("No Range to Iterate");//No I18N
        }

        this.startIndexOfRangesInCycle = new int[totalRanges];
        this.startIndexOfRangesInCycle[0] =  0;
        for(int i = 1; i < totalRanges; i++) {
            this.startIndexOfRangesInCycle[i] = ranges.get(i-1).getSize()+this.startIndexOfRangesInCycle[i-1];
        }
        this.sizeOfCycle = this.startIndexOfRangesInCycle[totalRanges - 1] + ranges.get(totalRanges - 1).getSize();

        this.ranges = ranges;
        this.isClockWise = isClockWise;
        this.indexInCycle = getIndexInCycle(sRangeIndex, sRowIndex, sColIndex);

        this.rangeIndex = sRangeIndex;
        setCurrentPosition();
    }

    private int getIndexInCycle(int sRangeIndex, int sRowIndex, int sColIndex) {
        if(sRangeIndex < 0 || sRangeIndex >= ranges.size()) {
            sRangeIndex = 0;
            sRowIndex = 0;
            sColIndex = 0;
        }

        DataRange dataRange = ranges.get(sRangeIndex);
        int adjustment = 0;

        if(sRowIndex < dataRange.getStartRowIndex()) {
            sRowIndex = dataRange.getStartRowIndex();
            sColIndex = dataRange.getStartColIndex();
            if(!this.isClockWise) {
                adjustment = -1;
            }
        }

        if(sRowIndex > dataRange.getEndRowIndex()) {
            sRowIndex = dataRange.getEndRowIndex();
            sColIndex = dataRange.getEndColIndex();
            if(this.isClockWise) {
                adjustment = 1;
            }
        }

        if(sColIndex < dataRange.getStartColIndex()) {
            sColIndex = dataRange.getStartColIndex();
            if(!this.isClockWise) {
                adjustment = -1;
            }
        }

        if(sColIndex > dataRange.getEndColIndex()) {
            sColIndex = dataRange.getEndColIndex();
            if(this.isClockWise) {
                adjustment = 1;
            }
        }

        int startIndexOfRangeInCycle = this.startIndexOfRangesInCycle[sRangeIndex];
        int rowCount = sRowIndex - dataRange.getStartRowIndex();
        int colCount = sColIndex - dataRange.getStartColIndex();

        return getIndexInCycle(startIndexOfRangeInCycle + rowCount*dataRange.getColSize() + colCount + adjustment, this.sizeOfCycle);
    }

    public static int getIndexInCycle(int virtualIndex, int sizeOfCycle) {
        if(sizeOfCycle == 0) {
            return virtualIndex;
        }
        if(virtualIndex < 0) {
            virtualIndex = Math.abs(virtualIndex);
            virtualIndex = sizeOfCycle - (virtualIndex % sizeOfCycle);
        }
        return virtualIndex % sizeOfCycle;
    }

    private void setCurrentPosition() {
        int index = this.rangeIndex;

        if(this.indexInCycle >= this.startIndexOfRangesInCycle[index]) {
            int elementsOnRight = this.startIndexOfRangesInCycle.length - 1 - index;
            while(elementsOnRight > 0 && this.indexInCycle >= this.startIndexOfRangesInCycle[index+1]) {
                index++;
                elementsOnRight--;
            }
        } else {
            int elementsOnLeft = index;
            while(elementsOnLeft > 0 &&  this.indexInCycle < this.startIndexOfRangesInCycle[index]) {
                index--;
                elementsOnLeft--;
            }
        }

        DataRange dataRange = this.ranges.get(index);
        int offset = this.indexInCycle - this.startIndexOfRangesInCycle[index];
        int rowOffset = offset / dataRange.getColSize();
        int colOffSet = offset % dataRange.getColSize();

        this.rangeIndex = index;
        this.rowIndex = dataRange.getStartRowIndex() + rowOffset;
        this.colIndex = dataRange.getStartColIndex() + colOffSet;

        if(!this.isAnyOneRowIteratedFromStartToFinish) {
            if(this.isRowStartedFromStart) {
                if(this.isEndOfRow()) {
                    this.isAnyOneRowIteratedFromStartToFinish = true;
                }
            } else if(isStartOfRow()) {
                if(this.isEndOfRow()) {
                    this.isAnyOneRowIteratedFromStartToFinish = true;
                } else {
                    this.isRowStartedFromStart = true;
                }
            }
        }
    }

    DataRange getDataRange() {
        return this.ranges.get(this.rangeIndex);
    }

    int getRowIndex() {
        return  this.rowIndex;
    }

    int getColIndex() {
        return this.colIndex;
    }

    boolean isCompleted() {
        return this.completedIteration >= this.sizeOfCycle;
    }

    void moveBy(int n) {
        if(n == 0) {
            return;
        }
        n = Math.abs(n);

        int virtualIndex;
        if(this.isClockWise) {
            virtualIndex = this.indexInCycle + n;
        } else {
            virtualIndex = this.indexInCycle - n;
        }
        this.indexInCycle = getIndexInCycle(virtualIndex, this.sizeOfCycle);
        this.completedIteration += n;
        setCurrentPosition();
    }

    /**
     * repetitions beyond current range, will be discarded
     * @param rowsRepeated
     * @return
     */
    int moveToEndOfLastRowOfRepetitions(int rowsRepeated) {
        int allowedRows = Math.min(rowsLeftInRange(), rowsRepeated - 1);
        moveBy(colsLeftInRow() + (allowedRows)*getDataRange().getColSize());
        return allowedRows;
    }

    /**
     * repetitions beyond current row, will be discarded
     * @param colsRepeated
     * @return
     */
    int moveToLastColOfRepetitions(int colsRepeated) {
        int allowedCols = Math.min(colsLeftInRow(), colsRepeated - 1);
        moveBy(allowedCols);
        return allowedCols;
    }

    void reset() {
        this.completedIteration =  0;
        this.isRowStartedFromStart = false;
        this.isAnyOneRowIteratedFromStartToFinish = false;
    }

    boolean isAnyOneRowIteratedFromStartToFinish() {
        return this.isAnyOneRowIteratedFromStartToFinish;
    }

    int colsLeftInRow() {
        DataRange dataRange = getDataRange();
        if(this.isClockWise) {
            return dataRange.getEndColIndex() - getColIndex();
        } else {
            return getColIndex() - dataRange.getStartColIndex();
        }
    }

    int rowsLeftInRange() {
        DataRange dataRange = getDataRange();
        if(this.isClockWise) {
            return dataRange.getEndRowIndex() - getRowIndex();
        } else {
            return getRowIndex() - dataRange.getStartRowIndex();
        }
    }

    boolean isStartOfRow() {
        DataRange dataRange = getDataRange();
        if(this.isClockWise) {
            return dataRange.getStartColIndex() == getColIndex();
        } else {
            return getColIndex() == dataRange.getEndColIndex();
        }
    }

    boolean isEndOfRow() {
        DataRange dataRange = getDataRange();
        if(this.isClockWise) {
            return dataRange.getEndColIndex() == getColIndex();
        } else {
            return getColIndex() == dataRange.getStartColIndex();
        }
    }

    public static void main(String[] args) {
//        List<DataRange> inputRanges = new ArrayList<>();
//        String activeCell = "#1.A1";
//        String[] dranges = {"#1.B3:D5", "#1.A1:B3"};
//        for(String drs: dranges) {
//            String[] splitForName = drs.split("\\.");
//            String[] splitForRange = splitForName[1].split(":");
//            inputRanges.add(new DataRange(splitForName[0], CellUtil.getRow(splitForRange[0]), CellUtil.getColumn(splitForRange[0]), CellUtil.getRow(splitForRange[1]), CellUtil.getColumn(splitForRange[1])));
//        }
//        System.out.println(inputRanges);
//
//        String[] splitForName = activeCell.split("\\.");
//        String activeasn = splitForName[0];
//        int activeRowIndex = CellUtil.getRow(splitForName[1]);
//        int activeColIndex = CellUtil.getColumn(splitForName[1]);
//
//        System.out.println(inputRanges);

//        String cell = "X1";
//        CyclicIterator cyIt = new CyclicIterator(inputRanges, 0, CellUtil.getRow(cell), CellUtil.getColumn(cell), false);
//        while (!cyIt.isCompleted()) {
//            System.out.println("isAnyOneRowIteratedFromStartToFinish:" + cyIt.isAnyOneRowIteratedFromStartToFinish());
//            System.out.println(cyIt.getDataRange().getAssociatedSheetName()+"."+ CellUtil.getCellReference(cyIt.getColIndex(), cyIt.getRowIndex()));
//            cyIt.moveBy(1);
//        }
    }

}
