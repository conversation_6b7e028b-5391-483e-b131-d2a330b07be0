// $Id$
package com.adventnet.zoho.websheet.model.actions.findAndReplace;

import com.adventnet.zoho.websheet.model.util.ActionConstants;

public enum FindScope {
    RANGE, ROW, COLUMN, SHEET, WORKBOOK;

    public static FindScope getZSFindScopeForAction(int traverseMode) {
        FindScope findScope = null;
        switch (traverseMode) {
            case ActionConstants.FIND_IN_WORKBOOK:
                findScope = WORKBOOK;
                break;
            case ActionConstants.FIND_IN_SHEET:
                findScope = SHEET;
                break;
            case ActionConstants.FIND_IN_RANGE:
                findScope = RANGE;
                break;
            case ActionConstants.FIND_IN_ROW:
                findScope = ROW;
                break;
            case ActionConstants.FIND_IN_COL:
                findScope = COLUMN;
                break;
            default:
                throw new IllegalArgumentException("traverseMode is not correct : " + traverseMode);//No I18N
        }
        return findScope;
    }
}
