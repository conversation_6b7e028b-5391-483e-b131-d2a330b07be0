// $Id$
package com.adventnet.zoho.websheet.model.actions.findAndReplace;

import com.adventnet.zoho.websheet.model.Annotation;
import com.adventnet.zoho.websheet.model.Cell;
import com.adventnet.zoho.websheet.model.CellImpl;
import com.adventnet.zoho.websheet.model.ReadOnlyCell;
import com.adventnet.zoho.websheet.model.RichStringProperties;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.util.ActionUtil;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.EngineUtils1;
import com.adventnet.zoho.websheet.model.util.RangeUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Replace {

    private String searchText;
    private String replaceText;
    private String zuid;
    private long exSharedLinkId;
    private boolean isMatchCase = true;

    public Replace(String searchText, String replaceText, String zuid, long exSharedLinkId) {
        nullOREmptyCheck(searchText);
        nullCheck(replaceText);
        this.searchText = searchText;
        setReplaceText(replaceText);
        this.zuid = zuid;
        this.exSharedLinkId = exSharedLinkId;
    }


    private void nullOREmptyCheck(String string) {
        nullCheck(string);
        if(string.length() == 0) {
            throw new IllegalArgumentException("search text can not be empty");//NO I18N
        }
    }

    private void nullCheck(String string) {
        if(string == null) {
            throw new IllegalArgumentException("search text can not be empty");//NO I18N
        }
    }

    private String replaceTextInString(String string) {
        string = Find.replaceNonBreakingSpaceCharWithSpaceChar(string);
        if(isMatchCase) {
            return string.replaceAll(Pattern.quote(searchText), Matcher.quoteReplacement(replaceText));
        } else {
            return string.replaceAll("(?i)"+ Pattern.quote(searchText), Matcher.quoteReplacement(replaceText));//No I18N
        }
    }

    private boolean replaceContent(FindResponseCellBean resultCell) {
        String valueString = resultCell.getCell().getCell().getValue().getValueString(resultCell.getCell().getCell().getSpreadsheetSettings());

        //Content of formula-Cell,array-cell and locked-cell should not be replaced
        if(!resultCell.getCell().getCell().isFormula()) {
            if (!((CellImpl) resultCell.getCell().getCell()).isArrayCell()) {
                if (!isInsideLockedRange(resultCell)
                        ) {
                    Cell cell = resultCell.getCell().getSheet().getCell(resultCell.getCell().getRowIndex(), resultCell.getCell().getColIndex());
                    String replacedString = replaceTextInString(valueString);
                    //List<RichStringProperties> richStringProperties = EngineUtils1.setLink(replacedString, resultCell.getCell().getSheet().getWorkbook());
                    ActionUtil.setCellValue(cell, replacedString, null,null,null,null,null, null);
                    return true;
                }
            }
        }
        return false;
    }

    private boolean replaceFormula(FindResponseCellBean resultCell) {
        boolean isArrayCell = ((CellImpl)resultCell.getCell().getCell()).isArrayCell();
        if(resultCell.getCell().getCell().isFormula() || isArrayCell) {
            String formula = resultCell.getCell().getCell().getLocalizedFormula();
            if(((CellImpl)resultCell.getCell().getCell()).isArrayCell()) {
                formula = formula.substring(1, formula.length() - 1); //got read of '{' and '}' in '{=A1}'
            }
            //formula of children-array-cells and locked-cell should not be replaced
            boolean isChildrenArrayCell = false;
            if(isArrayCell) {
                if(resultCell.getCell().getCell().getArrayRowSpan() == 0 && resultCell.getCell().getCell().getArrayRowSpan() == 0) {
                    isChildrenArrayCell = true;
                }
            }
            if(!isChildrenArrayCell) {
                if(!isInsideLockedRange(resultCell)) {
                    Cell cell = resultCell.getCell().getSheet().getCell(resultCell.getCell().getRowIndex(),resultCell.getCell().getColIndex());
                    int arrayRowSpan = 0;
                    int arrayColSpan = 0;
                    if(isArrayCell) {
                        arrayRowSpan = cell.getArrayRowSpan();
                        arrayColSpan = cell.getArrayColSpan();
                    }
                    ActionUtil.setCellValue(cell,replaceTextInString(formula));
                    if(isArrayCell) {
                        cell.setArraySpan(arrayRowSpan,arrayColSpan);
                    }
                    return true;
                }
            }
        }
        return false;
    }

    private void replaceAnnotation(FindResponseCellBean resultCell) {
        Annotation annotation = resultCell.getCell().getCell().getAnnotation();
        String content = annotation.getContent();
        content = replaceTextInString(content);
        annotation.setContent(content);
    }

    private void setReplaceText(String replaceText) {
        if(!isMatchCase) {
            replaceText = Matcher.quoteReplacement(replaceText);
        }
        this.replaceText = replaceText;
    }

    public void setMatchCase(boolean matchCase) {
        isMatchCase = matchCase;
    }

    public ReplaceResponseBean replace(List<FindResponseCellBean> findResponseCellBeans) {
        ReplaceResponseBean replaceResponseBean = new ReplaceResponseBean();
        replaceResponseBean.setFindResponseCellBeans(findResponseCellBeans);

        if(searchText.equals(this.replaceText)) {
            return replaceResponseBean;
        }

        List<Cell> replacedCells = new ArrayList<>();
        replaceResponseBean.setReplacedCells(replacedCells);

        for(FindResponseCellBean findResponseCellBean: findResponseCellBeans) {

            if(findResponseCellBean.getCell() == null) {
                continue;
            }

            if(isInsidePivotTargetRange(findResponseCellBean)) {
                continue;
            }

            if(isPicklistCell(findResponseCellBean)) {
                continue;
            }

            if (findResponseCellBean.isFoundInFormula()) {
                if (replaceFormula(findResponseCellBean)) {
                    replacedCells.add(findResponseCellBean.getCell().getSheet().getCell(findResponseCellBean.getCell().getRowIndex(),findResponseCellBean.getCell().getColIndex()));
                }
            } else if(findResponseCellBean.isFoundInContent()) {
                if(replaceContent(findResponseCellBean)) {
                    replacedCells.add(findResponseCellBean.getCell().getSheet().getCell(findResponseCellBean.getCell().getRowIndex(),findResponseCellBean.getCell().getColIndex()));
                }
            }
//            if(findResponseCellBean.isFoundInAnnotation()) {
//                replaceAnnotation(findResponseCellBean);
//            }
        }

        return replaceResponseBean;
    }

    private boolean isInsidePivotTargetRange(FindResponseCellBean findResponseCellBean) {
        List<PivotTable> pivotTables = findResponseCellBean.getCell().getSheet().getWorkbook().getPivotTables();
        if(pivotTables == null) {
            return false;
        }

        for(PivotTable pivotTable: pivotTables) {
            DataRange targetCellRange = pivotTable.getTargetCellRange();
            int startRowIndex = targetCellRange.getStartRowIndex();
            int startColIndex = targetCellRange.getStartColIndex();
            int endRowIndex = targetCellRange.getEndRowIndex();
            int endColIndex = targetCellRange.getEndColIndex();

            int rowIndex = findResponseCellBean.getCell().getRowIndex();
            int colIndex = findResponseCellBean.getCell().getColIndex();

            if(targetCellRange.getAssociatedSheetName().equals(findResponseCellBean.getCell().getSheet().getAssociatedName()) && rowIndex >= startRowIndex && rowIndex <= endRowIndex && colIndex >= startColIndex && colIndex <= endColIndex) {
                return true;
            }
        }
        return false;
    }

    private boolean isPicklistCell(FindResponseCellBean findResponseCellBean) {
        ReadOnlyCell cell = findResponseCellBean.getCell();
        if(cell == null) {
            return false;
        }

        return cell.getSheet().getPicklistIDForCell(cell.getRowIndex(),cell.getColIndex()) != -1;
    }

    private boolean isInsideLockedRange(FindResponseCellBean resultCell) {
        return RangeUtil.isRangeHasLockedRange(
                resultCell.getCell().getSheet(),
                new DataRange(
                        resultCell.getCell().getSheet().getAssociatedName(),
                        resultCell.getCell().getRowIndex(),
                        resultCell.getCell().getColIndex(),
                        resultCell.getCell().getRowIndex(),
                        resultCell.getCell().getColIndex()
                ),
                zuid,
                -1, exSharedLinkId);
    }

}
