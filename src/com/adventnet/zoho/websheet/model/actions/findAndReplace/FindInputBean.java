// $Id$
package com.adventnet.zoho.websheet.model.actions.findAndReplace;

import com.adventnet.zoho.websheet.model.ReadOnlyCell;
import com.adventnet.zoho.websheet.model.Sheet;
import com.adventnet.zoho.websheet.model.Workbook;
import com.adventnet.zoho.websheet.model.util.DataRange;
import com.adventnet.zoho.websheet.model.util.RangeUtil;
import com.adventnet.zoho.websheet.model.util.Utility;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class FindInputBean {

    private boolean isMatchCase = false;
    private boolean isMatchEntireCellContent = false;
    private boolean isMatchRegularExpression = false;
    private boolean isDirectionUp = false;
    private boolean isFindAll = false;

    private boolean isFindInContent = true;
    private boolean isFindInFormula = false;
    private boolean isFindInAnnotations = false;
    private boolean isFindInHiddenRows = false;
    private boolean isFindInHiddenColumns = false;

    private final String searchText;

    private final ReadOnlyCell activeCell = new ReadOnlyCell();

    private List<DataRange> dataRanges = new ArrayList<>();
    private int startRangeIndex;
    private int startRowIndex;
    private int startColIndex;
    private boolean isStartFromNextCell = true;

    public FindInputBean(String searchText, ReadOnlyCell activeCell) {
        if(searchText == null || searchText.length() == 0) {
            throw new IllegalArgumentException("search text can not be empty");//No I18N
        }
        if(activeCell == null) {
            throw new IllegalArgumentException("active cell can not be null");//No I18N
        }

        this.searchText = searchText;
        this.activeCell.setProperties(activeCell);
    }

    public boolean isMatchCase() {
        return isMatchCase;
    }

    public void setMatchCase(boolean matchCase) {
        isMatchCase = matchCase;
    }

    boolean isMatchEntireCellContent() {
        return isMatchEntireCellContent;
    }

    public void setMatchEntireCellContent(boolean matchEntireCellContent) {
        isMatchEntireCellContent = matchEntireCellContent;
    }

    boolean isMatchRegularExpression() {
        return isMatchRegularExpression;
    }

    public void setMatchRegularExpression(boolean matchRegularExpression) {
        isMatchRegularExpression = matchRegularExpression;
    }

    boolean isDirectionUp() {
        return isDirectionUp;
    }

    public void setDirectionUp(boolean directionUp) {
        isDirectionUp = directionUp;
    }

    boolean isFindAll() {
        return isFindAll;
    }

    public void setFindAll(boolean findAll) {
        isFindAll = findAll;
    }

    boolean isFindInContent() {
        return isFindInContent;
    }

    public void setFindInContent(boolean findInContent) {
        isFindInContent = findInContent;
    }

    boolean isFindInFormula() {
        return isFindInFormula;
    }

    public void setFindInFormula(boolean findInFormula) {
        isFindInFormula = findInFormula;
    }

    boolean isFindInAnnotations() {
        return isFindInAnnotations;
    }

    public void setFindInAnnotations(boolean findInAnnotations) {
        isFindInAnnotations = findInAnnotations;
    }

    boolean isFindInHiddenRows() {
        return isFindInHiddenRows;
    }

    public void setFindInHiddenRows(boolean findInHiddenRows) {
        isFindInHiddenRows = findInHiddenRows;
    }

    public boolean isFindInHiddenColumns() {
        return isFindInHiddenColumns;
    }

    public void setFindInHiddenColumns(boolean findInHiddenColumns) {
        isFindInHiddenColumns = findInHiddenColumns;
    }

    public String getSearchText() {
        return searchText;
    }

    public ReadOnlyCell getActiveCell() {
        return activeCell;
    }

    public static FindInputBean getFindInputBean(Sheet sheet, int activeRow, int activeCol, int traverseMode, String value, boolean isMatchCase, boolean isExactMatch, boolean isNext, boolean isFindInFormulae, List<DataRange> dataRanges) {

        /*
        dataAPIAction-Object may contain negative row/column.
        (row-index, col-index) for traverse-mode
            a. workbook (0, -1) , sheet-index will be 0
            b. sheet (0, -1)
            c. row (n,-1)
            d. col(-1,n)
         This should be taken care here.
         To-do: this checks should be removed after dataAPIAction-Object follows normal-action-Object
         */
        if(traverseMode == -1) {
            throw new IllegalArgumentException("traverse mode can not be null");//No I18N
        }
        FindScope findScope = FindScope.getZSFindScopeForAction(traverseMode);
        ReadOnlyCell activeCell = sheet.getReadOnlyCellFromShell(Math.max(activeRow, 0), Math.max(activeCol, 0));
         String findString = Utility.getDecodedString(value);
        FindInputBean findInputBean = new FindInputBean(findString, activeCell);
        findInputBean.setMatchCase(isMatchCase);
        findInputBean.setMatchEntireCellContent(isExactMatch);
        findInputBean.setFindInFormula(isFindInFormulae);

        findInputBean.setDirectionUp(!isNext);
        findInputBean.setDataRanges(dataRanges, findScope);

        return findInputBean;
    }


    public void setDataRanges(List<DataRange> selectedDataRanges, FindScope findScope) {
        //reset - active:cell to start or end of range if it's out of usedrowIndex or usedcollindex
        List<DataRange> dataRanges = getModifiedDRBasedOnScope(selectedDataRanges, findScope, this.activeCell);
        List<DataRange> uniqueDataRanges = getNonIntersectingDataRanges(dataRanges);

        Sheet sheet = this.activeCell.getSheet();
        String activeCellASN = sheet.getAssociatedName();
        this.startRowIndex = this.activeCell.getRowIndex();
        this.startColIndex = this.activeCell.getColIndex();

        int startRangeIndex = geEnclosingDataRangeIndex(uniqueDataRanges, activeCellASN, this.startRowIndex, this.startColIndex);
        List<Integer> completelyOutOfBoundDR = getCompletelyOutOfBoundDRAndTrimPartiallyOutOfBoundDR(uniqueDataRanges, sheet.getWorkbook());

        //remove Completely OutOfBound DataRanges
        int size = completelyOutOfBoundDR.size();
        for(int i = 0; i < size; i++) {
            Integer integer = completelyOutOfBoundDR.get(i);
            uniqueDataRanges.remove(integer - i);
        }

        this.dataRanges = uniqueDataRanges;

        if(!this.dataRanges.isEmpty()) {
            //adjust startRangeIndex based on Completely OutOfBound DataRanges
            int altStartRangeIndex = getAlteredStartRangeIndex(uniqueDataRanges, completelyOutOfBoundDR, startRangeIndex, this.isDirectionUp);
            if(altStartRangeIndex != startRangeIndex) {
                this.isStartFromNextCell = false;
            }

            // find new altStartRangeIndex, after the OutOfBound DataRanges deletion
            //completelyOutOfBoundDR : this is a sorted list in ascending order
            int count = 0;
            for (Integer integer : completelyOutOfBoundDR) {
                if(integer >= altStartRangeIndex) {
                    break;
                }
                count++;
            }
            altStartRangeIndex-=count;

            if(altStartRangeIndex < 0) {
                this.startRangeIndex = 0;
                DataRange dr = this.dataRanges.get(this.startRangeIndex);
                this.startRowIndex = dr.getStartRowIndex();
                this.startColIndex = dr.getStartColIndex();
            } else {
                this.startRangeIndex = altStartRangeIndex;
                DataRange dr = this.dataRanges.get(this.startRangeIndex);
                if (!this.isStartFromNextCell) {
                    if (this.isDirectionUp) {
                        this.startRowIndex = dr.getEndRowIndex();
                        this.startColIndex = dr.getEndColIndex();
                    } else {
                        this.startRowIndex = dr.getStartRowIndex();
                        this.startColIndex = dr.getStartColIndex();
                    }
                } else {
                    if (startRowIndex > dr.getEndRowIndex()) {
                        this.startRowIndex = dr.getEndRowIndex();
                        this.startColIndex = dr.getEndColIndex();
                        if (this.isDirectionUp()) {
                            this.isStartFromNextCell = false;
                        }
                    } else if (this.startColIndex > dr.getEndColIndex()) {
                        this.startColIndex = dr.getEndColIndex();
                        if (this.isDirectionUp()) {
                            this.isStartFromNextCell = false;
                        }
                    }
                }
            }
        }
    }

    private static int getAlteredStartRangeIndex(List<DataRange> uniqueDataRanges, List<Integer> completelyOutOfBoundDR, int startRangeIndex, boolean isDirectionUp) {
        int cycleSize = uniqueDataRanges.size();
        int count = cycleSize;
        while(count > 0) {
            count--;
            if(!completelyOutOfBoundDR.contains(startRangeIndex)) {
                break;
            }
            if(isDirectionUp) {
                startRangeIndex = CyclicIterator.getIndexInCycle(startRangeIndex - 1, cycleSize);
            } else {
                startRangeIndex = CyclicIterator.getIndexInCycle(startRangeIndex + 1, cycleSize);
            }
        }
        return startRangeIndex;
    }

    private static int geEnclosingDataRangeIndex(List<DataRange> uniqueDataRanges, String activeCellASN, int startRowIndex, int startColIndex) {
        int startRangeIndex = -1;
        int size = uniqueDataRanges.size();
        for(int i = 0; i < size; i++) {
            DataRange currentDR = uniqueDataRanges.get(i);
            if(currentDR.getAssociatedSheetName().equals(activeCellASN) &&
                    startRowIndex >= currentDR.getStartRowIndex() && startRowIndex <= currentDR.getEndRowIndex() &&
                    startColIndex >= currentDR.getStartColIndex() && startColIndex <= currentDR.getEndColIndex()) {
                startRangeIndex = i;
                break;
            }
        }
        return startRangeIndex;
    }

    private static List<Integer> getCompletelyOutOfBoundDRAndTrimPartiallyOutOfBoundDR(List<DataRange> uniqueDataRanges, Workbook workbook) {
        List<Integer> completelyOutOfBoundDR = new ArrayList<>();
        int size = uniqueDataRanges.size();
        for(int i = 0; i < size; i++) {
            DataRange currentDR = uniqueDataRanges.get(i);
            Sheet currentSheet = workbook.getSheetByAssociatedName(currentDR.getAssociatedSheetName());
            int usedRowIndex = currentSheet.getUsedRowIndex();
            int usedColIndex = currentSheet.getUsedColumnIndex();

            if(currentDR.getStartRowIndex() > usedRowIndex || currentDR.getStartColIndex() > usedColIndex) {
                completelyOutOfBoundDR.add(i);
                continue;
            }

            if(currentDR.getEndRowIndex() > usedRowIndex) {
                currentDR.setEndRowIndex(usedRowIndex);
            }

            if(currentDR.getEndColIndex() > usedColIndex) {
                currentDR.setEndColIndex(usedColIndex);
            }
        }
        return completelyOutOfBoundDR;
    }

    private static List<DataRange> getModifiedDRBasedOnScope(List<DataRange> selectedDataRanges, FindScope findScope, ReadOnlyCell activeCell) {
        List<DataRange> dataRanges = new ArrayList<>();
        Sheet sheet = activeCell.getSheet();
        switch (findScope) {
            case RANGE:
                dataRanges.addAll(selectedDataRanges);
                break;
            case ROW:
                if(selectedDataRanges.isEmpty()) {
                    DataRange operandRange = new DataRange(sheet.getAssociatedName(), activeCell.getRowIndex(), 0, activeCell.getRowIndex(), Utility.MAXNUMOFCOLS - 1);
                    dataRanges.add(operandRange);
                    break;
                } else {
                    dataRanges.addAll(selectedDataRanges.stream()
                            .map(dataRange -> new DataRange(dataRange.getAssociatedSheetName(), dataRange.getStartRowIndex(), 0, dataRange.getEndRowIndex(), Utility.MAXNUMOFCOLS - 1))
                            .collect(Collectors.toList()));
                    break;
                }
            case COLUMN:
                if(selectedDataRanges.isEmpty()) {
                    DataRange operandRange = new DataRange(sheet.getAssociatedName(), 0, activeCell.getColIndex(), Utility.MAXNUMOFROWS - 1, activeCell.getColIndex());
                    dataRanges.add(operandRange);
                    break;
                } else {
                    dataRanges.addAll(selectedDataRanges.stream()
                            .map(dataRange -> new DataRange(dataRange.getAssociatedSheetName(), 0, dataRange.getStartColIndex(), Utility.MAXNUMOFROWS - 1, dataRange.getEndColIndex()))
                            .collect(Collectors.toList()));
                    break;
                }
            case SHEET:
                dataRanges = new ArrayList<>();
                DataRange dataRangOfSheet = getDataRangOfSheet(sheet);
                if(dataRangOfSheet != null) {
                    dataRanges.add(dataRangOfSheet);
                }
                break;
            case WORKBOOK:
                dataRanges = new ArrayList<>();
                dataRanges.addAll(getDataRangesOfWorkbook(sheet.getWorkbook()));
                break;
            default:
                throw new IllegalArgumentException();
        }

        return dataRanges;
    }

    private static List<DataRange> getDataRangesOfWorkbook(Workbook workbook) {
        List<DataRange> sheetDataRanges = new ArrayList<>();
        for(Sheet sheet : workbook.getSheetList()) {
            DataRange dataRangOfSheet = getDataRangOfSheet(sheet);
            if(dataRangOfSheet != null) {
                sheetDataRanges.add(dataRangOfSheet);
            }
        }
        return sheetDataRanges;
    }

    private static DataRange getDataRangOfSheet(Sheet sheet) {
        if(sheet.isHidden()) {
            return null;
        }
        DataRange operandRange = new DataRange(sheet.getAssociatedName(), 0, 0, Utility.MAXNUMOFROWS - 1, Utility.MAXNUMOFCOLS - 1);
        return operandRange;
    }

    public List<DataRange> getDataRanges() {
        return this.dataRanges;
    }

    public int getStartRangeIndex() {
        return startRangeIndex;
    }

    public int getStartRowIndex() {
        return startRowIndex;
    }

    public int getStartColIndex() {
        return startColIndex;
    }

    public boolean isStartFromNextCell() {
        return isStartFromNextCell;
    }

    /**
     * order is maintained
     * @param input
     * @return list of Unique DataRange (i.e: no two dataRange intersects)
     */
    private static List<DataRange> getNonIntersectingDataRanges(List<DataRange> input) {
        List<DataRange> inputRanges = new ArrayList<>();
        if(input != null) {
            for (DataRange dr : input) {
                inputRanges.add(dr.clone());
            }
        }
        if(inputRanges.isEmpty()) {
            return inputRanges;
        }

        boolean isStartOver = true;
        while (isStartOver) {
            isStartOver = false;

            int noOfDataRange = inputRanges.size();
            for(int i = 0; i < noOfDataRange; i++) {
                for (int j = 0; j < noOfDataRange; j++) {
                    if (i == j) {
                        continue;
                    }

                    int firstIndex;
                    int secondIndex;
                    if (i < j) {
                        firstIndex = i;
                        secondIndex = j;
                    } else {
                        firstIndex = j;
                        secondIndex = i;
                    }
                    DataRange first = inputRanges.get(firstIndex);
                    DataRange second = inputRanges.get(secondIndex);
                    if(!first.getAssociatedSheetName().equals(second.getAssociatedSheetName())) {
                        continue;
                    }
                    List<DataRange> splitRanges = RangeUtil.splitRanges(Arrays.asList(first), second);
                    if(splitRanges.isEmpty()) {
                        isStartOver = true;
                        inputRanges.remove(firstIndex);
                    } else if(splitRanges.size() > 1){
                        isStartOver = true;
                        List<DataRange> sortedSplitRanges = RangeUtil.sortRowAscendingOrder(splitRanges);
                        inputRanges.set(firstIndex, sortedSplitRanges.get(0));
                        int size = sortedSplitRanges.size();
                        for(int n = 1; n < size; n++) {
                            inputRanges.add(firstIndex + n, sortedSplitRanges.get(n));
                        }
                    } else {
                        DataRange altFirst = splitRanges.get(0);
                        boolean isBothDiff = first.getStartRowIndex() != altFirst.getStartRowIndex() || first.getStartColIndex() != altFirst.getStartColIndex() ||
                                first.getEndRowIndex() != altFirst.getEndRowIndex() || first.getEndColIndex() != altFirst.getEndColIndex();
                        if(isBothDiff) {
                            isStartOver = true;
                            inputRanges.set(firstIndex, splitRanges.get(0));
                        }
                    }
                    if(isStartOver) {
                        break;
                    }
                }
                if(isStartOver) {
                    break;
                }
            }
        }
        return inputRanges;
    }
}
