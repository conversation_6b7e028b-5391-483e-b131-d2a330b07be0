//$Id$
package com.adventnet.zoho.websheet.model;

import com.adventnet.zoho.websheet.model.ErrorCode.DisplayType;
import com.adventnet.zoho.websheet.model.ErrorCode.MsgType;
import com.adventnet.zoho.websheet.model.TabInfo.TabType;
import com.adventnet.zoho.websheet.model.UserProfile.AccessType;
import com.adventnet.zoho.websheet.model.exception.AccessDeniedException;
import com.adventnet.zoho.websheet.model.exception.ActionTimeOutException;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.filter.FilterConstants;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;
import com.adventnet.zoho.websheet.model.response.ActionResponseObject;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.zs.ZSModelConstants;
import com.zoho.logs.common.util.logging.OnDemandSearchLogger;
import com.zoho.sheet.authorization.AppUtil;
import com.zoho.sheet.authorization.permissions.ActionPermissionCheck;
import com.zoho.sheet.exception.APIErrorHandler;
import com.zoho.sheet.util.ClientUtils;
import com.zoho.sheet.util.ZSLogger;

import javax.naming.LimitExceededException;
import java.util.Iterator;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 * <AUTHOR> N J ( ZT-0049 )
 */
public class ActionExecutor implements Runnable
{
	private static final Logger LOGGER = Logger.getLogger(ActionExecutor.class.getName());
	private WorkbookContainer wbContainer = null;

	private final String executorThreadNamePrefix = "Executor"; //No I18N
	private boolean isPaused = false;
	private ReentrantLock pauseLock = new ReentrantLock();
	private Condition unpaused = pauseLock.newCondition();

    public ActionExecutor(WorkbookContainer wbContainer)
    {
        this.wbContainer = wbContainer;
    }
    
    public WorkbookContainer getWorkbookContainer()
    {
    	return this.wbContainer;
    }
    
    private void setCurrentThreadName(JSONObjectWrapper actionObject)
    {
        Thread.currentThread().setName(this.getThreadName(actionObject));

    }
    private String getThreadName(JSONObjectWrapper actionObject)
    {
    	String tName = Thread.currentThread().getName();
        tName = tName.startsWith("Executor") ? tName.substring(0, tName.indexOf(ZSModelConstants.HASH)) : executorThreadNamePrefix + ZSModelConstants.HYPHEN + (++EngineFactory.executorThreadCounter); //No I18N
        return tName + "#RESOURCE_ID#"+ wbContainer.getResourceKey() + "#DOCID#" + wbContainer.getDocId() + "#AID#" + actionObject.getString(JSONConstants.ACTION_ID) + "#ACTION#" + actionObject.getString(JSONConstants.ACTION);	//No I18N
    }
    
    private JSONObjectWrapper getActionObjectFromQueue() throws InterruptedException
    {
        JSONObjectWrapper actionObject =  this.getWorkbookContainer().getActionQueue().poll(EngineConstants.DEFAULT_ACTION_QUEUE_TIMEOUT, TimeUnit.SECONDS); //wait till items gets available
        /*
        if(actionObject == null) {
            Workbook workbook = this.getWorkbookContainer().getWorkbookForSave();
            if (workbook != null && (workbook.isAIFormulaCellsInResponseList() || workbook.isAIFormulaCellsInEvaluationList())){

                LOGGER.log(Level.INFO, "[AI] Formula cells evaluation start");
                JSONObjectWrapper actionObjectTemp = new JSONObjectWrapper();
                actionObjectTemp.put(JSONConstants.ACTION_ID, this.getWorkbookContainer().getExecutedActionId());
                actionObjectTemp.put(JSONConstants.ZUID, this.getWorkbookContainer().getUserProfileList().get(0).getZUserId());
                ActionObject.getActionInfo(ActionConstants.LAZY_EVALUATION_CELLS_RESPONSE).addInJSONObj(actionObjectTemp);
                return actionObjectTemp;
            }
        }
        */
        return actionObject;
    }

    private void init(JSONObjectWrapper actionObject)
    {
    	ActionManager.initAllThreadLocal();
        AppUtil.initAllThreadLocal(actionObject);
        this.setCurrentThreadName(actionObject);
    }

    @Override
	public void run()
    {

        try
        {
            
        	JSONObjectWrapper actionObject = null;
            String collabId = wbContainer.getCollabId();
            MacroRecorder mr = null;
            UserProfile sUserProfile = null;
            
            int actionConstant = -1;
            String actionId = null;
            
            do
            {
                boolean isRollBack = false;
                String errMsg = null;
                DisplayType errDisplayType = DisplayType.DIALOG;
                MsgType errMsgType = MsgType.ERROR;
                String[] params = null;
                JSONObjectWrapper errObj = null;
                Long initStart = null;
                Long executionStart = null;
                Long doActionStart = null;
                Long doActionEnd = null;
                Long responseStart = null;
                Long newClientResponseEnd = null;
                boolean isFromPermissionCheck = false;
                
                try
                {
                    initStart = System.currentTimeMillis();
                    // before executing the Action ensure that it is not paused
                    beforeExecute();
                    actionObject = this.getActionObjectFromQueue();
                    if(actionObject == null)
                    {
                        break;
                    }
                    actionId = actionObject.getString("aid");
                    try
                    {
                        ActionPermissionCheck permissionCheck = ActionPermissionCheck.getInstance(wbContainer, actionObject);
                        if(permissionCheck != null)
                        {
                            permissionCheck.checkPermission();
                        }
                    }
                    catch(ProhibitedActionException e)
                    {
                        isFromPermissionCheck = true;
                        throw e;
                    }
                    catch(Exception e)
                    {
                        LOGGER.log(Level.SEVERE, "[AUTHORIZATION_FILTER][Exception] Exception while checking permission.", e);
                        LOGGER.log(Level.SEVERE, "[AUTHORIZATION_FILTER][Exception] Authorization check has been bypassed.", e); //No I18N
                    }
                    this.init(actionObject);
                    
                    
                    executionStart = System.currentTimeMillis();
                    if(actionObject.has(JSONConstants.TIME_STAMP))
                    {
                        long actionWaitingTime = executionStart - actionObject.getLong(JSONConstants.TIME_STAMP);
                        if(actionWaitingTime > EngineConstants.ACTION_WAITING_TIME_WARNING_LIMIT)
                        {
                            LOGGER.log(Level.INFO, "RESOURCE_ID: {0} >>> Waiting time for workbook Action ID {1} :: ActionConstant :: {2} ::::: {3}", new Object[] { 
                                wbContainer.getResourceKey(), actionObject.has("aid") ? actionObject.getInt("aid") : null, actionObject.getInt("a"), actionWaitingTime //No I18N
                            });
                        }
                    }
                    actionConstant = actionObject.getInt("a"); //No I18N
                    String sUserId = actionObject.getString("zuid"); // sender
                    
//                  [WMS_dependancy_removal]
                    String sRsid = null;
                    if(actionObject.has("rsid")){
                    	sRsid = actionObject.getString("rsid");
                    }
                    
                    String utid = actionObject.has(JSONConstants.UNIQUE_TAB_ID) ? actionObject.getString(JSONConstants.UNIQUE_TAB_ID) : null;
                    
                    //Getting tabType
                    String	 tabTypeStr	=	actionObject.has(JSONConstants.TAB_TYPE) ? actionObject.getString(JSONConstants.TAB_TYPE) : null;
                    TabType		tabType	=	ClientUtils.getTabType(tabTypeStr);
                    
                    // Code to execute Action
                    //logger.log(Level.INFO, "execute action JSON : {0}", actionObject);
                    //LOGGER.log(Level.INFO, "Discussion->ActionExecutor:action JSON: {0}", actionObject);
                    if(!isMutedAction(actionConstant))
                    {
                        boolean apiAction = actionObject.has(JSONConstants.API_ACTION);
                        sUserProfile = wbContainer.getUserProfile(sUserId); // sender
                        //adding -1 check for RSID, for the action which are not happened using Sheet UI
//                        WMS_dependancy_removal
                        if(sUserProfile != null && sRsid !=null && !sRsid.equals("-1"))
                        {
                            try
                            {
                                //mr = sUserProfile.getMacroRecorder(sRsid,tabType);
                                mr = sUserProfile.getTabInfo((utid == null) ? sRsid : utid, sRsid, tabType).getMacroRecorder();
                            }
                            catch(Exception ex)
                            {
                                LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: " + wbContainer.getResourceKey() + " Error in getting MacroRecorder.", ex);
                            }
                        }
                        
                        doActionStart = System.currentTimeMillis();

                        
                        ActionResponseObject responseObject = ActionManager.doAction(wbContainer, actionObject, mr, null,false);
                        
                        doActionEnd = System.currentTimeMillis();
                        long serverActionExecutionTime = doActionEnd - executionStart;

                        // Check if cell count limit exceed, then peoceed for rollback.
                            //later part should be done in else

                            // this can be null in the undo/redo cases
                            // TODO: we can have specific messages if there is null from undo/redo
                            if(responseObject != null)
                            {
                        	//Getting responseObject for handHeld device
                        	
//                                if(apiAction)
//                                {
                                    ContainerListener listener = wbContainer.getListener(Integer.parseInt(actionId));
                                    if(listener != null && apiAction)
                                    {
                                        LOGGER.log(Level.INFO, "notifying listener");
                                        JSONObjectWrapper apiResponse = responseObject.getAPIJSON();
                                        listener.notifyMessage(apiResponse);
                                    }
//                                }
                                //Will be available for Revert Block case.
//                                if(!responseObject.isErrorMessage())
//                                {
                                    //Handled dataValidation case in actionManager
//                                    if(responseObject.getDataValidationMessage() != null && responseObject.getDataValidationMessage().getBoolean(JSONConstants.DATAVALIDATION_ISSTOPEDIT))
//                                    {
//                                        wbContainer.rollbackAction(actionObject, responseObject.getDataValidationMessage().getString(JSONConstants.DATAVALIDATION_ERRORMSG), false);
//                                    }
                                    //adding -1 check for RSID, for the action which are not happened using Sheet UI
                                    //else if(sUserProfile != null && sRsid != null && !sRsid.equals("-1"
//                                    if(sUserProfile != null && ((sRsid != null && !sRsid.equals("-1")) || utid != null))
//                                    {
//                                        String undoRedoKey = (utid != null) ? utid : sRsid;
//                                        responseObject.setUndoRedoCountJson(wbContainer.getResourceKey(), undoRedoKey);
//                                    }
                                    responseStart = System.currentTimeMillis();
                                    //For handHeld device.
                                    MessagePropagator.dispatchMessage_ThroughWorker(wbContainer, responseObject, actionObject, listener);

                                    newClientResponseEnd = System.currentTimeMillis();
//                                }
//                                else
//                                {
                                    //sendErrorMessage(wbContainer, actionObject, responseObject.getErrorMessage(), MsgType.WARNING, DisplayType.DIALOG);
                                    //wbContainer.rollbackAction(actionObject, responseObject.getErrorMessage(), DisplayType.DIALOG);
//                                    isRollBack = true;
//                                    errMsg = responseObject.getErrorMessage();
//                                    errDisplayType = responseObject.getDisplayType() == null ? ErrorCode.DisplayType.DIALOG : responseObject.getDisplayType();
//                                    errMsgType = responseObject.getMsgType();
//                                    params = responseObject.getParams();
//                                }
                            }
//                            else
//                            {
                                //////////////////////////////////
                                //sendErrorMessage(wbContainer, actionObject, ErrorCode.ERROR_ACTION_EXECUTION, MsgType.ERROR, DisplayType.DIALOG);
                                //wbContainer.rollbackAction(actionObject, ErrorCode.ERROR_ACTION_EXECUTION, DisplayType.DIALOG);
//                                isRollBack = true;
//                                errMsg = ErrorCode.ERROR_ACTION_EXECUTION;
//                            }
                    }
                        
                    ////
//                    LOGGER.log(Level.INFO, "RESOURCE_ID: {0} >>> Total Time taken to Execute Action ID {1} :: ActionConstant :: {2} ::::: {3}   << ActionTime :: {4}  ~~~  ReevaluateTime :: {5}  ~~~  CalculateRowHeightsTime :: {6} >> ", new Object[]
//                        {
//                            wbContainer.getResourceKey(), actionId, actionConstant, (System.currentTimeMillis() - s), ActionManager.actionT.get(), ActionManager.reevaluateT.get(), ActionManager.calcRowHeightsT.get()
//                        });
                    //actionObject.set(JSONConstants.TIME_TAKEN, (System.currentTimeMillis() - s)); //No I18N
                }
                catch(ProhibitedActionException e)
                {
                    errMsg = e.getMessage();
                    LOGGER.log(Level.INFO,  "[errMsg]:::::{0}", errMsg);
                    notifyListener(actionObject, actionId, errMsg);
//                    LOGGER.info(" [errMsg]:::::"+errMsg+"::: isDefined::::"+ErrorCode.isDefined(errMsg));
                    if (errMsg != null && ErrorCode.isDefined(errMsg))
                    {
                        if (ErrorCode.DIALOG_ERRORS.contains(errMsg))
                        {
                           errObj = ErrorCode.getProhibitedDialogErrorMsg(e);
                        } else if (errMsg.equals(ErrorCode.ERROR_PIVOT_OVERWRITE_DATA) || errMsg.equals(ErrorCode.ERROR_PIVOT_OVERWRITE_DATA_MOVE) || errMsg.equals(ErrorCode.ERROR_PIVOT_TABLE_OVERLAPS_CREATE)
                                || errMsg.equals(ErrorCode.ERROR_PIVOT_TABLE_OVERLAP_WITH_SOURCE))
                        {
                            JSONObjectWrapper actionDataRange = null;
                            String dstnSheet = null;
                            if(actionObject.has(JSONConstants.DESTINATION_RANGELIST)) {
                                actionDataRange = ((JSONArrayWrapper)actionObject.optJSONArray(JSONConstants.DESTINATION_RANGELIST).get(0)).getJSONObject(0);
                                if(actionObject.has(JSONConstants.DESTINATION_SHEET_NAMES_LIST)) {
                                    dstnSheet = actionObject.optJSONArray(JSONConstants.DESTINATION_SHEET_NAMES_LIST).getJSONArray(0).getString(0);
                                }
                            }/*else 	if(actionObject.has("oldTarget")){
                            actionDataRange = ((JSONArrayWrapper)actionObject.optJSONArrayWrapper(JSONConstants.DESTINATION_RANGELIST).get(0)).getJSONObjectWrapper(0);
                            }*/
                            
                            if(actionDataRange!=null && dstnSheet!=null) {
                                String [] params1 = {dstnSheet +"."+CellUtil.getCellReference(actionDataRange.getInt(JSONConstants.START_COLUMN), actionDataRange.getInt(JSONConstants.START_ROW))};
                                errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.WARNING, DisplayType.DIALOG_YES_NO, params1);
                            }else {
                                errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.WARNING, DisplayType.DIALOG_YES_NO);
                            }
                            
                        } else if (errMsg.equals(ErrorCode.ERROR_ACTION_ID_MISMATCH))
                        {
                            // TODO: reloadLink should be done other way rather constructing the html tags here
                            String[] reloadLink =
                            {
                                "<a onclick='javascript:loadDocument(" + wbContainer.getDocId() + ");' href=''>", "</a>"
                            };
                            errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.ERROR, DisplayType.BANNER, reloadLink);
                            LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][GRID_ACTION][" + ErrorCode.ERROR_ACTION_ID_MISMATCH + "][RID:" + wbContainer.getResourceId() + "]");
                            //Action id mismatch in server. So notifying error to all the collaborators.
                            //Removing workbook container and MRUCache.
                            com.zoho.sas.l7.SessionEntityMap.remove(wbContainer.getDocId());
                            /////
                            EngineFactory.removeWorkbookContainer(wbContainer.getDocId(), sUserProfile.getAccessType());
                            
                        } else if (errMsg.equals(ErrorCode.ERROR_PR_PERMISSION_DENIED) || errMsg.equals(ErrorCode.ERROR_RANGE_LOCKED) || errMsg.equals(ErrorCode.ERROR_RANGE_OR_SHEET_LOCKED) || ErrorCode.UNDO_REDO_BLOCK_ERROR.contains(errMsg))
                        {
                            errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.WARNING, DisplayType.DIALOG);
                        }else if(errMsg.equals(ErrorCode.SKIP_PASTE)){
                        	LOGGER.log(Level.INFO, "PasteNOtAllowed");
                        }
                        else
                        {
                            errObj = ErrorCode.getErrorMessage(errMsg, null, MsgType.ERROR, DisplayType.BANNER);
                            LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][GRID_ACTION][" + errMsg + "][RID:" + wbContainer.getResourceId() + "]");
                            
                        }
                    } else if (errMsg != null && (errMsg.contains(ErrorCode.COPYCLIP_DOES_NOT_EXIST) || errMsg.contains(ErrorCode.ERROR_SHEET_LIMIT_EXCEED)
                            || errMsg.contains(ErrorCode.COPYCLIP_SOURCE_DOCUMENT_TRASHED) || errMsg.contains(ErrorCode.COPYCLIP_SOURCE_DOCUMENT_NOT_IN_SHARED_STATE)
                            || errMsg.contains(ErrorCode.ERROR_IMPORT_COL_LIMIT_EXCEED) || errMsg.contains(ErrorCode.ERROR_IMPORT_ROW_LIMIT_EXCEED)))
                    {
                        String[] errMsgSplit = errMsg.split("#");
                        errObj = ErrorCode.getErrorMessage(errMsgSplit[0], null, MsgType.WARNING, DisplayType.DIALOG, errMsgSplit[1]);
                    } else
                    {
                        errObj = ErrorCode.getErrorMessage(ErrorCode.ERROR_ADD_ACTION_TO_QUEUE, null, MsgType.ERROR, DisplayType.BANNER);
                        LOGGER.log(Level.INFO, "[ZS_ERROR_BANNER][GRID_ACTION][" + ErrorCode.ERROR_ADD_ACTION_TO_QUEUE + "][RID:" + wbContainer.getResourceId() + "]");
                    }
                    isRollBack = true;
                }
                catch (PivotUtil.PivotFailedException e){
                    isRollBack = true;
                    errMsg = ErrorCode.ERROR_PIVOT_TIMEOUT;
                    notifyListener(actionObject, actionId, errMsg);
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Pivot Refresh timed out. ActionID :: {3} :: ActionConstant :: {4}", new Object[] {
                            wbContainer.getResourceKey(), actionObject.get("zuid"), actionObject.has("rsid") ? actionObject.get("rsid") : "null", actionId, actionConstant //No I18N
                    });
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: " + wbContainer.getResourceKey() + " Pivot Refresh timed out", e);
                }
                catch(ReEvaluate.ReEvaluateFailedException e) {
                    isRollBack = true;
                    errMsg = ErrorCode.ERROR_REEVALUATE_FAILED;
                    notifyListener(actionObject, actionId, errMsg);
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Reevaluate timed out. ActionID :: {3} :: ActionConstant :: {4}", new Object[] {
                        wbContainer.getResourceKey(), actionObject.get("zuid"), actionObject.has("rsid") ? actionObject.get("rsid") : "null", actionId, actionConstant //No I18N
                    });
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: " + wbContainer.getResourceKey() + " Reevaluate timed out", e);
                }
                catch(AccessDeniedException ex) {
                		isRollBack = true;
                    errMsg = ErrorCode.ERROR_PUBLISH_ACCESSDENIED;
                    notifyListener(actionObject, actionId, errMsg);
//                    JSONObjectWrapper restrictedJson = actionObject.getJSONObjectWrapper(JSONConstants.RESTRICTION);
//                    if(restrictedJson.has(JSONConstants.RANGE_RESTRICTION)) {
//                    		JSONArrayWrapper rangeArray = restrictedJson.getJSONArrayWrapper(JSONConstants.RANGE_RESTRICTION);
//                    		for (int i = 0; i < rangeArray.length(); i++) {
//                    			JSONObjectWrapper json = rangeArray.getJSONObjectWrapper(0);
//                    			SheetRange range = new SheetRange(json.getInt(JSONConstants.START_ROW),json.getInt(JSONConstants.START_COLUMN),json.getInt(JSONConstants.END_ROW),json.getInt(JSONConstants.END_COLUMN));
//                    			errMsg = errMsg + "#" + range.toString();
//                    		}
//                    }
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Access Denied Exception . ActionID :: {3} :: ActionConstant :: {4}", new Object[]
                            {
                                wbContainer.getResourceKey(), actionObject.get("zuid"), actionObject.has("rsid") ? actionObject.get("rsid") : "null", actionId, actionConstant //No I18N
                            });
                }
                catch(Exception ex)
                {
                    //actionObject.set("err", "true"); //No I18N
                    //sendErrorMessage(wbContainer, actionObject, ErrorCode.ERROR_ACTION_EXECUTION, MsgType.ERROR, DisplayType.DIALOG);
                    //wbContainer.rollbackAction(actionObject, ErrorCode.ERROR_ACTION_EXECUTION, DisplayType.DIALOG);
                    isRollBack = true;
                    errMsg = ErrorCode.ERROR_ACTION_EXECUTION;
                    notifyListener(actionObject, actionId, ex.getMessage());
                    ////////
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: {0} :: USERID : {1} :: RSID : {2} >>> Error in executing action. ActionID :: {3} :: ActionConstant :: {4}", new Object[]
                        {
                            wbContainer.getResourceKey(), actionObject.get("zuid"), actionObject.has("rsid") ? actionObject.get("rsid") : "null", actionId, actionConstant //No I18N
                        });
                    LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: " + wbContainer.getResourceKey() + " Error in executing action.", ex);
                    
                    /*ContainerListener listener = wbContainer.getListener(Integer.parseInt(actionId));
                    if(listener != null && actionObject.has(JSONConstants.API_ACTION))
                    {
                        JSONObjectWrapper apiResponse = new JSONObjectWrapper();
                        apiResponse.put(JSONConstants.ERROR_MESSAGE, ex.getMessage() == null ? APIErrorHandler.INTERNAL_SERVER_ERR : ex.getMessage());
                        listener.notifyMessage(apiResponse);
                    }*/
                }
                finally
                {
                    ActionManager.removeAllThreadLocal();
                    Workbook workbook = wbContainer.getWorkbookForSave();
                    ContainerListener listener = wbContainer.getListener(Integer.parseInt(actionId));
                    boolean isFromPreProcessorOrPermissionCheck = (actionObject != null && actionObject.optBoolean(JSONConstants.IS_FROM_PREPROCESSOR, false)) || isFromPermissionCheck;

                    // Rollback is not needed when exceptions are thrown in preprocessor.
                    // Error messages needs to be sent for exceptions thrown from preprocessor and rollback action
                    if(isRollBack)
                    {
                        wbContainer.rollbackAction(actionObject, errMsg, isFromPreProcessorOrPermissionCheck);
                        errObj = errObj == null ? ErrorCode.getErrorMessage(errMsg, errMsg, errMsgType, errDisplayType, (Object[]) params) : errObj;
                        LOGGER.log(Level.WARNING, "ERROR RESOURCE_ID: {0} ErrorObj in executing action: {1}", new Object[]{wbContainer.getResourceKey(), errObj});
                        MessagePropagator.sendErrorMessage(wbContainer, workbook, listener, actionObject, errObj);
                    }


                    if(actionObject != null) {
                        if(initStart != null && executionStart != null && doActionStart != null && doActionEnd != null) {
                            long executionEnd = System.currentTimeMillis();
                            ZSLogger.log(
                                LOGGER,
                                Level.INFO, "Time taken in Executor for  >>>  RESOURCE_ID: {0}  >>>  ACTION_ID: {1}  >>>  ACTION_CONSTANT: {2}\n\t\tNo. of cells created till now before save: {3}\n\n\tTime taken for init: {4}\n\tmiscellaneous: {5}\n\tdoAction(): {6}\n\tmiscellaneous: {7}\n\t\n\t\tnew client response: {8}", //No I18N
                                new Object[] {
                                    this.wbContainer.getResourceKey(),
                                    actionObject.opt(JSONConstants.ACTION_ID),
                                    actionObject.opt(JSONConstants.ACTION),
                                    workbook == null ? "null" : workbook.noOfCellsCreatedAfterParse, //No I18N
                                    (executionStart - initStart),
                                    (doActionStart - executionStart),
                                    (doActionEnd - doActionStart),
                                    (executionEnd - doActionEnd),
                                    responseStart != null
                                        ? (newClientResponseEnd != null
                                            ? (newClientResponseEnd - responseStart)
                                            : "[[[NOT LOGGABLE]]]") //No I18N
                                        : "[[[NOT LOGGABLE]]]" //No I18N
                                },
                                new ZSLogger.ZSCustomAppLogFieldKey[] {ZSLogger.ZSCustomAppLogFieldKey.TIME_TAKEN},
                                new Object[] {executionEnd - initStart}
                            );
                        } else {
                            ZSLogger.log(LOGGER, Level.INFO, "Time taken in Executor for  >>>  RESOURCE_ID: {0}  >>>  ACTION_ID: {1}  >>>  ACTION_CONSTANT: {2}  \n\t\tnoOfCellsCreated: {3} \n>>>  [[[NOT LOGGABLE]]]  >>> InitStart: {4}  >>> executionStart: {5}  >>> doActionStart: {6}  >>> doActionEnd: {7}\n\t\tnew client response: {8}\n {9}", //No I18N
                                new Object[]{
                                this.wbContainer.getResourceKey(),
                                    actionObject.opt(JSONConstants.ACTION_ID),
                                    actionObject.opt(JSONConstants.ACTION),
                                    workbook == null ? "null" : workbook.noOfCellsCreatedAfterParse, //No I18N
                                    initStart,
                                    executionStart,
                                    doActionStart,
                                    doActionEnd,
                                    responseStart != null
                                        ? (newClientResponseEnd != null
                                            ? (newClientResponseEnd - responseStart)
                                            : "[[[NOT LOGGABLE]]]") //No I18N
                                        : "[[[NOT LOGGABLE]]]", //No I18N
                                    workbook == null ? "" : (workbook.noOfCellsCreatedAfterParse > 500 ? "too many cells created" : "") //No I18N
                                },
                                new ZSLogger.ZSCustomAppLogFieldKey[] {},
                                new Object[] {}
                            );
                        }
                    }

                    if(actionObject != null)
                    {
                        wbContainer.addExecutedAction(actionObject);
                        //////////
                        if(!isRollBack
                            && sUserProfile!= null
                            && sUserProfile.getAccessType() != AccessType.PUBLIC_EXTERNAL
                            && sUserProfile.getAccessType() != AccessType.PUBLIC_ORG
                            && sUserProfile.getAccessType() != AccessType.RANGE_PUBLIC_EXTERNAL
                            && sUserProfile.getAccessType() != AccessType.RANGE_PUBLIC_ORG
                            && sUserProfile.getAccessType() != AccessType.SHEET_PUBLIC_EXTERNAL
                            && sUserProfile.getAccessType() != AccessType.SHEET_PUBLIC_ORG)
                        {
                            // Save call only for specific actions if the action is not rollled back
                            checkIfSaveRequired(actionObject);
                        }
                    }
                }
            }
            while(true);
        }
        finally
        {
            wbContainer.setIsExecuting(false);
        }
    }

    public void notifyListener(JSONObjectWrapper actionObject, String actionId, String errMsg) {
        if(actionObject.optBoolean(JSONConstants.API_ACTION, false)) {
            try
            {
                ContainerListener listener = wbContainer.getListener(Integer.parseInt(actionId));
                if (listener != null) {
                    LOGGER.log(Level.INFO, "notifying listener for error case");
                    JSONObjectWrapper apiResponse = new JSONObjectWrapper();
                    apiResponse.put(JSONConstants.ERROR_MESSAGE, errMsg != null ? errMsg : String.valueOf(APIErrorHandler.INTERNAL_SERVER_ERR));
                    listener.notifyMessage(apiResponse);
                }
            } catch (Exception e) {
                LOGGER.log(Level.SEVERE, "error while notifying listener", e);
            }
        }
    }

    private void checkIfSaveRequired(JSONObjectWrapper actionObject)
    {
        int actionConstant = actionObject.getInt(JSONConstants.ACTION);
        switch(actionConstant)
        {
//            case ActionConstants.UNDO:
//            case ActionConstants.REDO:
//            case ActionConstants.SHEET_INSERT:
//            case ActionConstants.SHEET_DUPLICATE:
//            case ActionConstants.SHEET_COPY:
//            case ActionConstants.SHEET_MOVE:
//            case ActionConstants.SHEET_REMOVE:
//            case ActionConstants.SHEET_RENAME:
//            case ActionConstants.SHEET_TABCOLOR:
//            case ActionConstants.FORM_CREATE:
//            case ActionConstants.CONVERT_FORM:
//            case ActionConstants.DOCUMENT_SETTING:
            case ActionConstants.REVERT_VERSION:
//            case ActionConstants.IMPORT_INSERT_AS_NEW_SHEETS:
//            case ActionConstants.IMPORT_REPLACE_SPREADSHEET:
//            case ActionConstants.IMPORT_REPLACE_DATA_STARTING_AT_CELL:
//            case ActionConstants.IMPORT_APPEND_ROWS_TO_CURRENTSHEET:
//            case ActionConstants.IMPORT_REPLACE_CURRENTSHEET:
//            case ActionConstants.SERVERCLIP_PASTE_SHEET:    
//            case ActionConstants.SAVE_RTL_SHEET:
                // We call Save in ActionExecutor for this action so the ChangeLog appear properly in right Version.
                String versionLabel = actionObject.has(JSONConstants.VERSION_LABEL) ? actionObject.getString(JSONConstants.VERSION_LABEL) : null;
//                EngineFactory.saveThreadPool.submit(new WorkbookSaveHandler(wbContainer, versionLabel));
//                EngineFactory.saveThreadPool.execute(new WorkbookSaveExecutor(wbContainer, versionLabel, true));
                wbContainer.save(versionLabel, null, true);
                break;
            default:
                break;
        }
    }

    /**
     * Some action doesn't require all collaborators to be notified. Only the sender / owner of the action can know about the changes.
     * <br>
     * All collaborators will receive the document response
     * <p>
     * <b>Note : Add actions further in the list to avoid collaborations for those actions.</b>
     *
     * <AUTHOR> N J ( ZT-0049 )
     * @param senderZUID
     * @param receiverZUID
     * @param action - Action to check if collaboration is required via wms server.
     * @return true if collaboration is required, false if document response.
     */
    public static Boolean isActionResponseRequired(WorkbookContainer container, String senderZUID, String receiverZUID, Integer action)
    {
        if(FilterConstants.FILTER_ACTIONS.contains(action))
        {
            return senderZUID.equals(receiverZUID);
        }
        return true;
    }

    private boolean isMutedAction(int action)
    {
        return action == ActionConstants.OVERWRITE_API_PREPROCESS
                || action == ActionConstants.SEND_SPREADSHEET_IN_MAIL;
    }
    
    public static int estimateStringifiedLength(JSONObjectWrapper jsonObject) throws LimitExceededException
    {
        int jsonLength = 0;
        Iterator iterate = jsonObject.keys();
        while (iterate.hasNext())
        {
            String key = String.valueOf(iterate.next());
            jsonLength += key.length();

            Object valueObject = jsonObject.get(key);
            if (valueObject instanceof JSONObjectWrapper)
            {
                jsonLength += estimateStringifiedLength((JSONObjectWrapper) valueObject);
            }
            else if (valueObject instanceof JSONArrayWrapper)
            {
                jsonLength += estimateStringifiedLength((JSONArrayWrapper) valueObject);
            }
            else
            {
                jsonLength += estimateStringifiedLength(valueObject);
            }

            //Meta chars
            //1. "
            //2. "
            //3. :
            //4. ,
            jsonLength += 4; // "<key>":<value>,
        }

        //Meta chars
        //1. {
        //2. }
        jsonLength += 1; // (+2-1) {pairs}  // subtract 1 character for last comma

        if (jsonLength > Constants.MAX_RESPONSE_SIZE)
        {
            OnDemandSearchLogger.log(LOGGER, Level.SEVERE, "[RESPONSE][Exception] Exception JSONObjectWrapper length {0} exceeded maximum response size", new Object[]{jsonLength});
            throw new LimitExceededException("[RESPONSE][Exception] Maximum Response Size has been exceeded."); //No I18N
        }

        return jsonLength;
    }

    private static int estimateStringifiedLength(JSONArrayWrapper jsonArray) throws LimitExceededException
    {
        int jsonLength = 0;
        Iterator iterate = jsonArray.iterator();
        while (iterate.hasNext())
        {
            Object element = iterate.next();
            if (element instanceof JSONObjectWrapper)
            {
                jsonLength += estimateStringifiedLength((JSONObjectWrapper) element);
            } 
            else if (element instanceof JSONArrayWrapper)
            {
                jsonLength += estimateStringifiedLength((JSONArrayWrapper) element);
            } 
            else
            {
                jsonLength += estimateStringifiedLength(element);
            }

            //Meta chars
            //1. ,
            jsonLength += 1; // <element>,
        }

        //Meta chars
        //1. [
        //2. ]
        jsonLength += 1; // (+2-1) [<elements>]  // subtract 1 character for last comma

        if (jsonLength > Constants.MAX_RESPONSE_SIZE)
        {
            OnDemandSearchLogger.log(LOGGER, Level.SEVERE, "[RESPONSE] Exception JSONArrayWrapper Length {0} exceeded maximum response size", new Object[]{jsonLength});
            throw new LimitExceededException("max response size exceeded!!! ");//No I18N
        }

        return jsonLength;
    }

    private static int estimateStringifiedLength(Object object) throws LimitExceededException
    {
        //Meta chars
        //1. "
        //2. "
        int jsonLength = String.valueOf(object).length() + 2; // "<value>"

        if (jsonLength > Constants.MAX_RESPONSE_SIZE)
        {
            OnDemandSearchLogger.log(LOGGER, Level.SEVERE, "[RESPONSE] Exception String length {0} exceeded maximum response size", new Object[]{jsonLength});
            throw new LimitExceededException("max response size exceeded!!! ");//No I18N
        }

        return jsonLength;
    }

	private void beforeExecute()
	{
		pauseLock.lock();
		try
		{
			while(isPaused)
			{
				unpaused.await();
			}
		}
		catch(InterruptedException ie)
		{
			//t.interrupt();
		}
		finally
		{
			pauseLock.unlock();
		}
	}

	public void pause()
	{
		pauseLock.lock();
		try
		{
			isPaused = true;
		}
		finally
		{
			pauseLock.unlock();
		}
	}

	public void resume()
	{
		pauseLock.lock();
		try
		{
			isPaused = false;
			unpaused.signalAll();
		}
		finally
		{
			pauseLock.unlock();
		}
	}

	public boolean isPaused()
	{
		return isPaused;
	}
}
