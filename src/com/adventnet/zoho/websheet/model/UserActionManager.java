/* $Id$ */

package com.adventnet.zoho.websheet.model;

import com.adventnet.iam.ContactAPI;
import com.adventnet.iam.IAMProxy;
import com.adventnet.iam.IAMUtil;
import com.adventnet.iam.User;
import com.adventnet.zoho.websheet.model.Book.BookType;
import com.adventnet.zoho.websheet.model.Cell.Type;
import com.adventnet.zoho.websheet.model.TabInfo.TabType;
import com.adventnet.zoho.websheet.model.action.EmptyCellsInRange;
import com.adventnet.zoho.websheet.model.actions.findAndReplace.FindInputBean;
import com.adventnet.zoho.websheet.model.celledithistory.CellEditHistoryUtil;
import com.adventnet.zoho.websheet.model.exception.ProhibitedActionException;
import com.adventnet.zoho.websheet.model.ext.functions.RangeFunctions;
import com.adventnet.zoho.websheet.model.externalRange.ExternalRangeUtils;
import com.adventnet.zoho.websheet.model.externalRange.WorkbookLink;
import com.adventnet.zoho.websheet.model.nlp.NLInteractorImpl;
import com.adventnet.zoho.websheet.model.parser.ClipboardRenderer;
import com.adventnet.zoho.websheet.model.parser.HTMLRenderer;
import com.adventnet.zoho.websheet.model.parser.SimpleHTMLRenderer;
import com.adventnet.zoho.websheet.model.pivot.PivotTable;
import com.adventnet.zoho.websheet.model.pivot.PivotUtil;
import com.adventnet.zoho.websheet.model.response.v2.util.ResponseUtils;
import com.adventnet.zoho.websheet.model.response.viewport.Area;
import com.adventnet.zoho.websheet.model.response.viewport.Constraints;
import com.adventnet.zoho.websheet.model.style.zstheme.ZSThemesLibrary;
import com.adventnet.zoho.websheet.model.usersettings.UserSpreadSheetSettings;
import com.adventnet.zoho.websheet.model.util.*;
import com.adventnet.zoho.websheet.model.util.RangeUtil.SheetRange;
import com.zoho.sheet.action.externaldata.ExternalDataUtils;
import com.zoho.sheet.authorization.util.AuthorizationUtil;
import com.zoho.sheet.chart.TableConstraints;
import com.zoho.sheet.chart.TablePrediction;
import com.zoho.sheet.chartengine.ChartEngineManager;
import com.zoho.sheet.columnStats.CSAction;
import com.zoho.sheet.datarefineries.DataRefineryUtils;
import com.zoho.sheet.datarefineries.datacleaning.DataCleaningMessageConstants;
import com.zoho.sheet.deluge.DelugeUtils;
import com.zoho.sheet.listingpage.UserInfo;
import com.zoho.sheet.navigator.NavObject;
import com.zoho.sheet.navigator.actionhandlers.NavUserActionHandler;
import com.zoho.sheet.navigator.constants.NavUserActionConstant;
import com.zoho.sheet.navigator.dataholder.NavUserActionDataHolder;
import com.zoho.sheet.navigator.responsegenerator.NavUserActionResponseGenerator;
import com.zoho.sheet.parse.HtmlParserConstant;
import com.zoho.sheet.parse.HtmlToJSON;
import com.zoho.sheet.sheetzia.ZSZiaUtilities;
import com.zoho.sheet.tools.JsonWatcher;
import com.zoho.sheet.util.*;
import com.zoho.sheet.zia.ZiaManager;
import com.zoho.sheet.zia.spellcheck.SpellCheckUtil;
import com.zoho.zfsng.client.ResourceInfo;
import com.zoho.zfsng.client.ZohoFS;
import com.zoho.zfsng.constants.ShareConstants;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.adventnet.zoho.websheet.model.ErrorCode.ERROR_USER_SETTINGS_FETCH;
import static com.adventnet.zoho.websheet.model.ErrorCode.ERROR_USER_SETTINGS_SAVE;

/**
 * <AUTHOR>
 */
public class UserActionManager
{

	static final Logger LOGGER = Logger.getLogger(JsonWatcher.class.getName());

	public static HashMap<String, Object> doAction(WorkbookContainer wbcontainer, Workbook workbook, JSONObjectWrapper actionJson) throws Exception
	{
		long timeStamp = System.currentTimeMillis();
		HashMap<String, Object> responseMap = new HashMap<>();
		int majorAction = actionJson.getInt("a"); // No I18N
		String tabTypeStr = actionJson.has("tabType") ? actionJson.getString("tabType") : "";    //No I18N
		TabType tabTypeObj = ClientUtils.getTabType(tabTypeStr);
		List<Range> listOfRanges = ActionJsonUtil.getListOfRangesFromJsonObject(workbook, actionJson, false);
		int startRow = -1;
		int startCol = -1;
		int endRow = -1;
		int endCol = -1;

		if(listOfRanges != null)
		{
			Range range = listOfRanges.get(0);
			startRow = range.getStartRowIndex();
			startCol = range.getStartColIndex();
			endRow = range.getEndRowIndex();
			endCol = range.getEndColIndex();
		}

		Sheet activeSheet = actionJson.has(JSONConstants.CURRENT_ACTIVE_SHEET) ? workbook.getSheetByAssociatedName(actionJson.getString(JSONConstants.CURRENT_ACTIVE_SHEET)) : actionJson.has(JSONConstants.SHEETLIST) ? workbook.getSheetByAssociatedName(ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST))) : null;

		UserProfile uProfile = CurrentRealm.getUserProfile();
		User user = IAMUtil.getCurrentUser();
		String docid = wbcontainer.getDocId();
		String ownerId;
		String dataGenerationType=null;
		long exSharedLinkId = actionJson.optLong(JSONConstants.EXTERNAL_SHARE_LINK_ID);
		switch(majorAction)
		{
			case ActionConstants.AUTO_SUM:
				responseMap.put("range", RangeFunctions.getStatusBarFunctionResults(listOfRanges, true));
				break;
			case ActionConstants.COUNT_EMPTY_CELLS:
//                CountEmptyCellsInRange countEmptyCellsInRange = new CountEmptyCellsInRange(listOfRanges.get(0).toDataRange());
//                System.out.println(listOfRanges.get(0).toDataRange());
				Sheet sheet = listOfRanges.get(0).getSheet();
				DataRange datRange = listOfRanges.get(0).toDataRange();
				if(datRange.getStartRowIndex() == 0 && datRange.getEndRowIndex() == (Utility.MAXNUMOFROWS - 1))
				{
					datRange = new DataRange(sheet.getAssociatedName(), datRange.getStartRowIndex(), datRange.getStartColIndex(), sheet.getUsedRowIndex(), datRange.getEndColIndex());
				}
				EmptyCellsInRange eCount = new EmptyCellsInRange(datRange);
				eCount.execute(workbook);
				Collection<RangeUtil.SheetRange> emptyCellsRange = eCount.getResult();
				List<Integer> listEmptyRows = new ArrayList<>();
				for(RangeUtil.SheetRange sRange : emptyCellsRange)
				{
					for(int i = sRange.getStartRowIndex(); i <= sRange.getEndRowIndex(); i++)
					{
						listEmptyRows.add(i);
					}
				}
				responseMap.put("count", listEmptyRows);
				break;
			case ActionConstants.ALT_EQUAL_RANGE:
				responseMap.put("range", CellUtil.getAltEqualRange(activeSheet, startRow, startCol, true));   //Note : Need to send the last parameter once we consider columns in the view port // No I18N
				break;
			case ActionConstants.GET_ACTION_LIST:
				if(actionJson.has("FROM_VERSION")) {
					responseMap.put("lists", ActionUtil.getListsForAuditTrail(wbcontainer, actionJson.getBoolean("withMeta"), actionJson.getBoolean("showAll"), actionJson.has("TO_VERSION") ? actionJson.getString(JSONConstants.VERSION) : "", actionJson.getString("FROM_VERSION")));
				} else {
					responseMap.put("lists", ActionUtil.getListsForAuditTrail(wbcontainer, actionJson.getBoolean("withMeta"), actionJson.getBoolean("showAll"), actionJson.has(JSONConstants.VERSION) ? actionJson.getString(JSONConstants.VERSION) : ""));
				}
				break;
			case ActionConstants.SERVERCLIP_CHECK_LOCALE:
				String zuid = actionJson.getString("zuid");
				JSONObjectWrapper serverClipObject = ActionUtil.getServerClipObject(zuid);
				boolean isSameLocale = true;
				if(serverClipObject != null)
				{
					String version = serverClipObject.optString(JSONConstants.VERSION, null);
					version = version != null && version.isEmpty() ? null : version;
					String docId = serverClipObject.getString(JSONConstants.DOC_ID);
					String rid = serverClipObject.getString(JSONConstants.RID);
					int lastExecutedActionID;
					if(version == null)
					{
						lastExecutedActionID = serverClipObject.getInt(JSONConstants.EXECUTED_ACTION_ID_WHILE_COPY);
					}
					else
					{
						lastExecutedActionID = -1;
					}
					String docOwner = serverClipObject.getString(JSONConstants.OWNER);
					Workbook srcWorkBook = (Workbook) wbcontainer.getSourceBooksForPaste(docId, rid, docOwner, version, zuid, lastExecutedActionID).get(BookType.WORKBOOK);
					isSameLocale = ActionUtil.checkLocale(srcWorkBook, workbook);
				}
				responseMap.put(JSONConstants.IS_SAME_LOCALE, isSameLocale);
				break;
			case ActionConstants.SERVERCLIP_CLIPBOARD_COMPARE:
				boolean isSameContent = ActionUtil.compareClipContentWithServerClip(wbcontainer, actionJson.getString("zuid"), actionJson.getString("clipContent"));
				responseMap.put(JSONConstants.IS_SAME_CONTENT, isSameContent);
				break;
			case ActionConstants.GET_MARKDOWN_DATA:
				dataGenerationType = "md";//No I18N
			case ActionConstants.GET_TSV_DATA:
				actionJson.set(JSONConstants.FUNCTION_LOCALE, workbook.getSpreadsheetSettings().getLocale().getLanguage());
				boolean isCopyRestricted = false;
				if(actionJson.has(JSONConstants.COPY_OFF))
				{
					isCopyRestricted = actionJson.optBoolean(JSONConstants.COPY_OFF);
				}
				endRow = Math.min(activeSheet.getUsedRowIndex(), endRow);
				endCol = Math.min(activeSheet.getUsedColumnIndex(), endCol);
				String versionNumber;
				try{
					versionNumber = CurrentRealm.getWorkBookIdentity();
				}
				catch(Exception e){
					versionNumber = null;
				}
				long cellSize = (long) (endRow - startRow + 1) * (endCol - startCol + 1);
				if(cellSize > EngineConstants.DATA_RANGE_GENERATION_LIMIT){
					responseMap.put("range", ""); // No I18N
					responseMap.put("error","CONVERSION.LIMIT_EXCEED");//NO I18N
				}else{
					ClipboardRenderer dataRenderer = null;
					if("md".equalsIgnoreCase(dataGenerationType)){ //NO I18N
						//MarkDown Generation
						dataRenderer = ClipboardRenderer.getInstance(activeSheet, startRow, startCol, endRow, endCol, false,true, wbcontainer, versionNumber);
					}else{
						//TSV Generation
						dataRenderer = ClipboardRenderer.getInstance(activeSheet, startRow, startCol, endRow, endCol, true,false, wbcontainer, versionNumber);
					}
					responseMap.put("range", dataRenderer.render(isCopyRestricted)); // No I18N
				}
				break;
			case ActionConstants.SERVERCLIP_COPY_RANGE://COPY_TO_CLIP
			case ActionConstants.COPY_TO_CLIP:
				actionJson.set(JSONConstants.FUNCTION_LOCALE, workbook.getSpreadsheetSettings().getLocale().getLanguage());
				int lastExecutedActionId = wbcontainer.getExecutedActionId();
                /*
                if(actionJson.has(JSONConstants.VERSION_NO)) {
                    JSONObjectWrapper versionInfo = new JSONArrayWrapper(ZohoFS.getVersionInfo(wbcontainer.getDocsSpaceId(), wbcontainer.getResourceId(), actionJson.getString(JSONConstants.VERSION_NO))).getJSONObject(0);
                    String zfsngVersionId = versionInfo.getString(JSONConstants.VERSION_ID);
                    Long versionActionsResId = DocumentUtils.getVersionIdforZFSNGVersion(Long.valueOf(wbcontainer.getDocId()), wbcontainer.getDocOwner(), zfsngVersionId);
                    List<String> versionActionsList = EngineUtils1.loadActionsList(wbcontainer, versionActionsResId, ZSStore.FileName.VACTIONS);
                    String lastActionObjectInVersionStr = versionActionsList.get(versionActionsList.size() - 1);
                    JSONObjectWrapper lastActionObjectInVersion = new JSONObjectWrapper(lastActionObjectInVersionStr);
                    lastExecutedActionId = lastActionObjectInVersion.getInt(JSONConstants.ACTION_ID);
                } else {
                    lastExecutedActionId = wbcontainer.getExecutedActionId();
                }
                */

                /*
                    Clip will have both "version" and "xaidc" of the source.
                    But priority will be given to the version. In the clip, even though xaidc is present, if version is present, workbook state type will be taken as "version" and not "aid" when pasting.
                 */
				JSONObjectWrapper serverclip = ActionUtil.createServerClipForRange(wbcontainer.getResourceKey(), docid, wbcontainer.getDocOwner(), workbook, lastExecutedActionId, majorAction, actionJson.optString("zuid", null), actionJson.optString(JSONConstants.VERSION_NO, null), actionJson.optJSONArray(JSONConstants.RANGELIST), actionJson.optJSONArray(JSONConstants.SHEETLIST), actionJson.optString(JSONConstants.SHEET_NAME), actionJson.optString(JSONConstants.FUNCTION_LOCALE, null), actionJson.optString(JSONConstants.RSID, null));//No I18N
				//boolean _isDownloadOffAndCopyOn = ZSZFSNGUtils.isDownloadOffAndCopyOn(actionJson.optString("zuid", null), wbcontainer.getResourceId()); //NO I18N
				boolean isCopyOff = false;
				if(actionJson.has(JSONConstants.COPY_OFF))
				{
					isCopyOff = actionJson.optBoolean(JSONConstants.COPY_OFF);
				}
				else
				{
					isCopyOff = false;
				}
				//boolean _isDownloadOffAndCopyOn = (actionJson.optBoolean(JSONConstants.DOWNLOAD_RESTRICTION && )ZSZFSNGUtils.isDownloadOffAndCopyOn(actionJson.optString("zuid", null), wbcontainer.getResourceId()); //NO I18N
				if(!actionJson.has(JSONConstants.ONLY_CLIP_TO_SERVER))
				{
					boolean isTabSeparatedValuesRequird = actionJson.getBoolean(JSONConstants.IS_REQUIRED_TAB_SEPARATED_VALUES);
					String versionNo;
					try
					{
						versionNo = CurrentRealm.getWorkBookIdentity();
					}
					catch(Exception e)
					{
						versionNo = null;
					}
					ClipboardRenderer clipboardRenderer = ClipboardRenderer.getInstance(activeSheet, startRow, startCol, endRow, endCol, isTabSeparatedValuesRequird, false,wbcontainer, versionNo);
					boolean isHtml = (clipboardRenderer instanceof HTMLRenderer || clipboardRenderer instanceof SimpleHTMLRenderer);
					boolean isRemoteDocument = wbcontainer.isRemoteMode();
					clipboardRenderer.setHost(actionJson.getString("host"), isCopyOff, isRemoteDocument);
					responseMap.put("isHtml", isHtml);
					responseMap.put("range", clipboardRenderer.render(isCopyOff)); // No I18N
					responseMap.put("serverclip", getServerClip(serverclip));
//	                if(majorAction == ActionConstants.SERVERCLIP_COPY_RANGE){
//		            	ActionUtil.createServerClipForRange(docid, actionJson);
//	                }
				}
				JSONObjectWrapper json = new JSONObjectWrapper();
				JSONArrayWrapper rangeArray = actionJson.optJSONArray(JSONConstants.RANGELIST);
				JSONArrayWrapper listOfASNs = actionJson.optJSONArray(JSONConstants.SHEETLIST);
				JSONArrayWrapper rangeList = new JSONArrayWrapper(rangeArray.getJSONArray(0).toString());
				JSONArrayWrapper newRangeList = new JSONArrayWrapper();
				for(int i = 0; i < rangeList.length(); i++)
				{
					JSONObjectWrapper range = rangeList.getJSONObject(i);
					range.put(JSONConstants.START_ROW, range.getInt(JSONConstants.START_ROW) + 1);
					range.put(JSONConstants.START_COLUMN, range.getInt(JSONConstants.START_COLUMN) + 1);
					range.put(JSONConstants.END_ROW, range.getInt(JSONConstants.END_ROW) + 1);
					range.put(JSONConstants.END_COLUMN, range.getInt(JSONConstants.END_COLUMN) + 1);
					newRangeList.put(range);
				}
				json.put(JSONConstants.ASSOCIATED_SHEET_NAME, listOfASNs.getJSONArray(0).get(0));
				json.put(JSONConstants.RANGE_TO_COPY, newRangeList.get(0));
				actionJson.put(JSONConstants.SOURCE_RANGE, json);
				ActionUtil.addUserServerClip(wbcontainer, actionJson, majorAction);
				break;

			case ActionConstants.SERVERCLIP_CUT_RANGE:
				actionJson.set(JSONConstants.FUNCTION_LOCALE, workbook.getSpreadsheetSettings().getLocale().getLanguage());
				JSONObjectWrapper clipObj = ActionUtil.createServerClipForRange(wbcontainer.getResourceKey(), docid, wbcontainer.getDocOwner(), workbook, wbcontainer.getExecutedActionId(), majorAction, actionJson.optString("zuid", null), actionJson.optString(JSONConstants.VERSION, null), actionJson.optJSONArray(JSONConstants.RANGELIST), actionJson.optJSONArray(JSONConstants.SHEETLIST), actionJson.optString(JSONConstants.SHEET_NAME), actionJson.optString(JSONConstants.FUNCTION_LOCALE, null), actionJson.optString(JSONConstants.RSID, null));// No I18N

				if(!actionJson.has(JSONConstants.ONLY_CLIP_TO_SERVER))
				{
					boolean isTabSeparatedValuesRequird = actionJson.getBoolean(JSONConstants.IS_REQUIRED_TAB_SEPARATED_VALUES);
					endRow = Math.min(activeSheet.getUsedRowIndex(), endRow);
					endCol = Math.min(activeSheet.getUsedColumnIndex(), endCol);

					String versionNo;
					try
					{
						versionNo = CurrentRealm.getWorkBookIdentity();
					}
					catch(Exception e)
					{
						versionNo = null;
					}
					ClipboardRenderer clipboardRenderer = ClipboardRenderer.getInstance(activeSheet, startRow, startCol, endRow, endCol, isTabSeparatedValuesRequird, false, wbcontainer, versionNo);
					boolean isHtml = (clipboardRenderer instanceof HTMLRenderer || clipboardRenderer instanceof SimpleHTMLRenderer);
					boolean isRemoteDocument = wbcontainer.isRemoteMode();
					clipboardRenderer.setHost(actionJson.getString("host"), false, isRemoteDocument);
					responseMap.put("isHtml", isHtml);
					responseMap.put("range", clipboardRenderer.render(false)); // No I18N
				}

				responseMap.put("sheetserverclip", getServerClip(clipObj));
				break;
			case ActionConstants.SERVERCLIP_COPY_SHEET:


				actionJson.set(JSONConstants.FUNCTION_LOCALE, workbook.getSpreadsheetSettings().getLocale().getLanguage());
				JSONObjectWrapper serverclipObj = ActionUtil.createServerClipForSheet(wbcontainer.getResourceKey(), docid, wbcontainer.getDocOwner(), wbcontainer.getExecutedActionId(), majorAction, actionJson.optString("zuid", null), actionJson.optBoolean(JSONConstants.COPY_OFF), ActionJsonUtil.constructStringList(actionJson.getJSONArray(JSONConstants.SHEETS_TO_COPY)), actionJson.optJSONArray(JSONConstants.SHEETLIST), actionJson.optString(JSONConstants.SHEET_NAME), actionJson.optString(JSONConstants.FUNCTION_LOCALE, null), actionJson.optString(JSONConstants.RSID, null));// No I18N


				responseMap.put("sheetserverclip", getServerClip(serverclipObj));
				break;
			case ActionConstants.SERVERCLIP_REMOVE:
//            	String puid = ActionUtil.getPasteUserId();
				zuid = actionJson.getString("zuid");
				JSONObjectWrapper clipInfo = ActionUtil.removeServerClipObject(actionJson.getString("from"), zuid);
				responseMap.put("sheetserverclip", getServerClip(clipInfo));
				break;
			case ActionConstants.SERVERCLIP_GET_REFSHEETS:
//            	puid = ActionUtil.getPasteUserId();
				zuid = actionJson.getString("zuid");
				List<String> sheetList = new ArrayList<>();
				Workbook tempWorkbook = null;
				List<String> ascNames = new ArrayList<>();
				if(actionJson.getString("from").equals(JSONConstants.DESTINATION_DOC))
				{
					serverClipObject = ActionUtil.getServerClipObject(zuid);

					if(serverClipObject == null)
					{
						throw new Exception(ErrorCode.COPYCLIP_DOES_NOT_EXIST);
					}
//            		tempWorkbook = (wbcontainer.getSourceWorkbookForPaste() != null) ? wbcontainer.getSourceWorkbookForPaste() : EngineUtils1.getWorkBook(serverClipObject.getString(JSONConstants.DOC_ID), serverClipObject.getString(JSONConstants.RID), null);
					String versionCopied = serverClipObject.optString(JSONConstants.VERSION, null);
					versionCopied = (versionCopied != null && versionCopied.isEmpty()) ? null : versionCopied;
					int xaidc = versionCopied == null ? serverClipObject.getInt(JSONConstants.EXECUTED_ACTION_ID_WHILE_COPY) : -1;
					tempWorkbook = (Workbook) wbcontainer.getSourceBooksForPaste(serverClipObject.getString(JSONConstants.DOC_ID), serverClipObject.getString(JSONConstants.RID), serverClipObject.getString(JSONConstants.OWNER), versionCopied, serverClipObject.optString(JSONConstants.ZUID, zuid), xaidc).get(BookType.WORKBOOK);
					ascNames = JSONArrayWrapper.toStringList(serverClipObject.getJSONArray(JSONConstants.SHEETS_TO_COPY));
				}
				else
				{
					ascNames.add(activeSheet.getAssociatedName());
					tempWorkbook = workbook;
				}
				if(!tempWorkbook.isDependenciesUpdated()) {
					tempWorkbook.updateCellDependencies();
				}
				sheetList = ActionUtil.getReferredSheetList(tempWorkbook, ascNames, zuid);
				responseMap.put("refsheetList", ActionUtil.changeAscNamestoNames(tempWorkbook, sheetList));
				break;

			case ActionConstants.GET_USER_CONTACT:
//                LOGGER.info(" GET_USER_CONTACT called [actionJson]::"+actionJson);
				String mode = actionJson.getString("iamApiMode"); //Utility.getDecodedString( request.getParameter("iamApiMode"));
				LOGGER.info(" [API][MODE] :::::" + mode);
				User _currUser = IAMUtil.getCurrentUser();
				long _zUID = _currUser.getZUID();
//              int _startIndex     = Integer.parseInt(request.getParameter("startIndex"));
				int _limit = 50;
				if("GET".equals(mode))
				{
					responseMap.put("USER_CONTACT", ZSIAmUtil.getUserContact(_zUID, 0, 50));
				}
				else if("SEARCH".equals(mode))
				{
//                  ZSIAmUtil.searchContacts(currUser.getZUID(), "bha", ContactAPI.SEARCH_CONTAINS, null, true, 0, 50);
					JSONObjectWrapper _contactJSONObj = ZSIAmUtil.searchContacts(_zUID, actionJson.getString("searchString"), ContactAPI.SEARCH_CONTAINS, null, true, actionJson.getInt("startIndex"), _limit); //No I18N
					responseMap.put("USER_CONTACT", _contactJSONObj);
				}
				break;
			/*case ActionConstants.GET_ORG_MEMBERS:
                LOGGER.info(" GET_ORG_MEMBERS called [actionJson]::"+actionJson);
				responseMap.put("ORG_MEMBERS", WorkDriveInfoUtils.getActiveTeamMembers(wbcontainer.getResourceId()));
				break;*/
			case ActionConstants.SHEET_COPY_CREATE_NEW_SPREADSHEET:
//            	puid = ActionUtil.getPasteUserId(); 
				zuid = actionJson.getString("zuid");
//            	workbook.setDocumentId(docid);
            	boolean copyRefSheet =actionJson.getBoolean(JSONConstants.COPY_REFERRED_SHEETS);
            	String newRID = ActionUtil.copySheetToNewWorkbook(workbook, wbcontainer, activeSheet.getAssociatedName(), copyRefSheet, zuid);
            	responseMap.put("newRID", newRID);
            	break;
            case ActionConstants.PICK_LIST:
                responseMap.put("range", ActionUtil.getPickList(activeSheet, startRow, startCol, actionJson.getBoolean(JSONConstants.IS_TYPE_AHEAD))); // No I18N
                break;
            case ActionConstants.TEXTTOCOLUMNS_PREVIEW:
                int startsFromRow = actionJson.getInt(JSONConstants.TTC_STARTS_FROM_ROW);
                JSONArrayWrapper ttcPreviewDetails = ActionUtil.doTextToColumns_Split(activeSheet,startRow, startCol, endRow, endCol, actionJson.getString(JSONConstants.TTC_DELIMITERS), actionJson.getString(JSONConstants.TTC_ENCLOSURE), actionJson.getBoolean(JSONConstants.TTC_ISMERGEDDELIMITERS_CHECKED), actionJson.getString(JSONConstants.VALUE), startsFromRow, true);
                responseMap.put("range1", ttcPreviewDetails.get(0)); // No I18N
                responseMap.put("range2", ttcPreviewDetails.get(1)); // No I18N
                // System.out.println("UserActionManager...." + ttcPreviewDetails.get(2));
                responseMap.put("range3", ttcPreviewDetails.get(2)); // No I18N
                break;
            case ActionConstants.GET_NEWLY_ADDED_USER:
	            User userObj = IAMProxy.getInstance().getUserAPI().getUserFromZUID(actionJson.getString(JSONConstants.ID));
	            String fullName = userObj.getFullName();
	            String name = userObj.getDisplayName();
	            String email = userObj.getPrimaryEmail();
	            long ZUID = userObj.getZUID();
	            JSONObjectWrapper rObj = new JSONObjectWrapper();
	            rObj.put("fullname", fullName); //No I18N
	            rObj.put("name", name); //No I18N
	            rObj.put("id", ZUID); //No I18N
	            rObj.put("email", email); //No I18N
	            responseMap.put("newUserDetails", rObj); //No I18N
                break;
            case ActionConstants.FIND:
                List<DataRange> listOfDataRanges = new ArrayList<>();
                for(Range range : listOfRanges)
                {
                    listOfDataRanges.add(range.toDataRange());
                }
                FindInputBean findInputBean = FindInputBean.getFindInputBean(activeSheet,actionJson.optInt(JSONConstants.CURRENT_ACTIVE_ROW, startRow),actionJson.optInt(JSONConstants.CURRENT_ACTIVE_COLUMN, startCol), actionJson.optInt(JSONConstants.TRAVERSE_MODE,-1),actionJson.optString(JSONConstants.VALUE, null), actionJson.optBoolean(JSONConstants.IS_CASE_SENSITIVE, false),actionJson.optBoolean(JSONConstants.IS_EXACT_MATCH,false), actionJson.optBoolean("in", false), actionJson.optBoolean(JSONConstants.IS_FIND_IN_FORMULAE, false), listOfDataRanges); //No I18N
                ReadOnlyCell findResponseCell = ActionUtil.find(findInputBean);
                responseMap.put("range", findResponseCell);  // No I18N

				if(actionJson.has(JSONConstants.GET_NO_OF_MATCH))
				{
					boolean isGetNoOfMatch = actionJson.getBoolean(JSONConstants.GET_NO_OF_MATCH);
					if(isGetNoOfMatch)
					{
						if(findResponseCell != null)
						{
							int noOfMatchesAtLeft = 0;
							int noOfMatchesAtRight = 0;

							List<DataRange>[] split = RangeUtil.split(findInputBean.getDataRanges(), findResponseCell);
							List<Cell> leftMatches = ActionUtil.findAll(findInputBean, split[0]);
							noOfMatchesAtLeft = leftMatches.size();

							List<Cell> rightMatches = ActionUtil.findAll(findInputBean, split[1]);
							noOfMatchesAtRight = rightMatches.size();

							responseMap.put(JSONConstants.MATCH_AT, noOfMatchesAtLeft + 1);
							responseMap.put(JSONConstants.NO_OF_MATCH, noOfMatchesAtLeft + 1 + noOfMatchesAtRight);
						}
						else
						{
							responseMap.put(JSONConstants.MATCH_AT, 0);
							responseMap.put(JSONConstants.NO_OF_MATCH, 0);
						}
                    }
                }
                break;
            case ActionConstants.GOAL_SEEK: //144
                JSONObjectWrapper jObj = ActionUtil.goalSeek(workbook, actionJson.optString("vcsn", null),actionJson.optString("tcsn", null),actionJson.optString("dv", null),actionJson.optString("vc", null),actionJson.optString("tc", null));// No I18N
                responseMap.put("responseJson", jObj); // No I18N
                break;
            case ActionConstants.NAMEDRANGE_READ:
                if(!workbook.isDependenciesUpdated())
                {
                    workbook.updateCellDependencies();
                }
                responseMap.put("range", NamedExpression.getRange(workbook.getNamedExpression(actionJson.getString("nor")), activeSheet,startRow, startCol)); // No I18N
                break;
			case ActionConstants.NAMEDRANGE_DETAILS_READ:
				responseMap.put("namedRanges", ResponseUtils.getNameRangeDetails(workbook));//No I18N
				break;
            case ActionConstants.CTRL_NAVIGATION:
                int row = startRow;
                int col = startCol;
                int travesalMode = actionJson.getInt("tm");
                int resultIndex = EngineUtils1.getCellIndex(activeSheet, row, col, travesalMode); 
				switch (travesalMode)
				{
					case 1:
					case 2:
						row = resultIndex;
						break;
					case 3:
					case 4:
						col = resultIndex;
						break;
				}
                
                responseMap.put("row", row); // No I18N
                responseMap.put("col", col); // No I18N
                break;
            case ActionConstants.SORT_DETAILS:
                responseMap.put("responseArray", addExpandSelectionDetails(activeSheet,startRow, startCol, endRow, endCol));
                break;
            case ActionConstants.WEBDATA_LIST:
                Object webDataList;
                if(wbcontainer.isGdriveDoc() || wbcontainer.getResourceId() == null || wbcontainer.isRemoteMode())
                {
                    webDataList = WebDataUtils.getWebDataList(workbook, actionJson.getString("rid"));
                }else {
                     webDataList = ExternalDataUtils.getWebDataList(workbook, actionJson.getString("rid"));
                }
            	responseMap.put("webDataList", webDataList);// No I18N
                break;
            case ActionConstants.CLOUD_DATA_OBSTRUCTS:
                List<RangeUtil.SheetRange> obstructingRanges = ExternalDataUtils.getObstructingRanges(workbook, actionJson.getString(JSONConstants.WEBDATA_ID), actionJson.getInt(JSONConstants.ERROR_CODE));
                JSONArrayWrapper rangeArr = new JSONArrayWrapper();
                for(RangeUtil.SheetRange range: obstructingRanges) {
                    JSONArrayWrapper array = new JSONArrayWrapper();
                    array.put(range.getStartRowIndex()).put(range.getStartColIndex()).put(range.getEndRowIndex()).put(range.getEndColIndex());
                    rangeArr.put(array);
                }
                responseMap.put(JSONConstants.RANGES, rangeArr);
                break;
            case ActionConstants.GET_SCHEDULER_DETAILS:
                JSONArrayWrapper schedulerInfoList = ExternalDataUtils.getSchedulerInfos(workbook, wbcontainer);
                responseMap.put("schedulerInfoList", schedulerInfoList);
                break;
			case ActionConstants.DATA_CLEANING_INFO:
				long time = System.currentTimeMillis();
				JSONObjectWrapper dcParams = actionJson.getJSONObject(JSONConstants.DATA_CLEANING_PARAMS);
				dcParams.put(DataCleaningMessageConstants.CELL_RANGE, new JSONArrayWrapper().put(startRow).put(startCol).put(endRow).put(endCol));
				JSONObjectWrapper dcResponse = DataRefineryUtils.getDataCleaningInfo(activeSheet, dcParams);
				responseMap.put(JSONConstants.DATA_CLEANING_RESPONSE, dcResponse);
				if (System.currentTimeMillis() - time > 2000) {
					LOGGER.log(Level.WARNING, "[Data_Cleaning slowness] Time taken to execute data cleaning info : {0}", System.currentTimeMillis() - time);
				}
				break;
			case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE:
			case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET:
			case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK:
				String rep = ConditionFormatUtils.readConditionalFormat(workbook, activeSheet, actionJson.getString("dateFormat"), actionJson.getString("timeFormat"), startRow, startCol, endRow, endCol, majorAction);
				responseMap.put("responsecf", rep);// No I18N
				if(majorAction == ActionConstants.CONDITIONAL_FORMAT_READ_RANGE)
				{
					if(rep.equals("{}"))
					{
						String dtype = RangeUtil.getDataType(listOfRanges).name().toLowerCase();
						responseMap.put("dtype", dtype);// No I18N
					}
				}
				break;
			case ActionConstants.CONDITIONAL_FORMAT_READ_RANGE_FOR_MOBILE:
			case ActionConstants.CONDITIONAL_FORMAT_READ_SHEET_FOR_MOBILE:
			case ActionConstants.CONDITIONAL_FORMAT_READ_WORKBOOK_FOR_MOBILE:
				String res = ThemeJsonConvertor.readConditionalFormat(workbook, activeSheet, actionJson.getString("dateFormat"), actionJson.getString("timeFormat"), startRow, startCol, endRow, endCol, majorAction);
				responseMap.put("responsecf", res);// No I18N
				if(majorAction == ActionConstants.CONDITIONAL_FORMAT_READ_RANGE_FOR_MOBILE)
				{
					if(res.equals("{}"))
					{
						String dtype = RangeUtil.getDataType(listOfRanges).name().toLowerCase();
						responseMap.put("dtype", dtype);// No I18N
					}
				}
				break;
			case ActionConstants.CONDITIONAL_FORMAT_READ_DATATYPE:
				String dtype = RangeUtil.getDataType(listOfRanges).name().toLowerCase();
				responseMap.put("dtype", dtype);// No I18N
				break;
//            case ActionConstants.CONDITIONAL_FORMAT_READ_EDITOBJ: 
            case ActionConstants.HIDDEN_ROW_DATA:
                responseMap.put("responseJson", ActionUtil.getRowData(activeSheet, startRow));
                break;
            case ActionConstants.READ_PIVOT_FIELDS:
				PivotTable pivotTable = actionJson.has(JSONConstants.PIVOT_ID) ? workbook.getDataPivotTable(actionJson.getString(JSONConstants.PIVOT_ID)) : null;
				if(actionJson.has(JSONConstants.NAME_OF_RANGE))
				{
					String sourceName = actionJson.getString(JSONConstants.NAME_OF_RANGE);
					Table table = TableUtil.getTable(workbook, sourceName);

					if(table != null) {
						responseMap.put("responseJson", ActionUtil.getPivotFields(table,pivotTable));
					}
					else {
						NamedExpression namedExpression = workbook.getNamedExpression(actionJson.getString(JSONConstants.NAME_OF_RANGE), activeSheet);
						if (namedExpression.isRange()) {
							Range range = NamedExpression.getRange(namedExpression, workbook.getSheet(0), 0, 0);
							responseMap.put("responseJson", ActionUtil.getPivotFields(range.getSheet(), range.getStartRowIndex(), range.getStartColIndex(), range.getEndRowIndex(), range.getEndColIndex(),pivotTable));
						}
					}
				}
				else {
					Sheet srcSheet =  workbook.getSheetByAssociatedName(ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST)));
					responseMap.put("responseJson", ActionUtil.getPivotFields(srcSheet, startRow, startCol, endRow, endCol,pivotTable));
				}
                break;
			case ActionConstants.READ_POSSIBLE_PIVOTS:
				String pivotName = null;
				List<String> connected = new ArrayList<>();
				SlicerBody slicer;
				if(actionJson.getString(JSONConstants.SLICER_ID).startsWith("Slicer")){
					slicer = workbook.getSlicer(actionJson.getString(JSONConstants.SLICER_ID));
				}
				else{
					slicer = workbook.getTimeLineSlicer(actionJson.getString(JSONConstants.SLICER_ID));
				}
				if(slicer != null) {
					pivotName = slicer.getConnection().getTablesNames().get(0);
					connected = slicer.getConnection().getTablesNames();
				}

				if(pivotName != null) {
					pivotTable = workbook.getDataPivotTable(pivotName);
					if(pivotTable != null) {
						String sourceRangeAddress = pivotTable.getSourceCellRange(workbook).toString();
						List<String> commonPivotsList = SlicerUtil.getPivotsOfCommonSource(workbook, sourceRangeAddress);
						List<Integer> status = SlicerUtil.getStatusForCommonPivots(workbook, commonPivotsList, connected);

						responseMap.put("pivots", commonPivotsList);
						responseMap.put("status", status);
					}
				}
				break;
			case ActionConstants.GET_PIVOT_FORMULA:
				String pivotID = actionJson.getString(JSONConstants.PIVOT_ID);
				int pivotRow = actionJson.getInt(JSONConstants.START_ROW);
				int pivotCol = actionJson.getInt(JSONConstants.START_COLUMN);
				boolean isSameSheet = actionJson.getBoolean(JSONConstants.IS_SAME_SHEET);
				responseMap.put("pivotFormula", PivotUtil.getPivotFormula(workbook, pivotID, pivotRow, pivotCol,isSameSheet));
				break;
			case ActionConstants.UPDATE_TIMELINE_SCROLL:
				String slicerName = actionJson.getString(JSONConstants.SLICER_ID);
				String scrollDate = actionJson.getString(JSONConstants.SCROLL_DATE);
				TimelineUtil.updateTimelineScroll(workbook,slicerName,scrollDate);

				break;
			case ActionConstants.UPDATE_SLICER_SCROLL:
				slicerName = actionJson.getString(JSONConstants.SLICER_ID);
				int scrollItem = actionJson.getInt(JSONConstants.SCROLL_INDEX);
				SlicerUtil.updateSlicerScroll(workbook,slicerName,scrollItem);

				break;
            case ActionConstants.READ_PIVOT_FIELD_MIN_MAX:
				Sheet srcSheet =  workbook.getSheetByAssociatedName(ActionJsonUtil.getFirstSheetFromJsonArray(actionJson.optJSONArray(JSONConstants.SHEETLIST)));
                responseMap.put("responseJson", PivotUtil.getFieldMinMaxValue(srcSheet, startCol, startRow, endRow));
                break;
            case ActionConstants.READ_RECOMMENDED_PIVOT:
            	 responseMap.put("responseJson",PivotUtil.getRecommendedPivot(wbcontainer, activeSheet, actionJson));
                break;
            case ActionConstants.DETECT_PIVOT_RANGE:
            	startRow = actionJson.getInt(JSONConstants.CURRENT_ACTIVE_ROW);
            	startCol = actionJson.getInt(JSONConstants.CURRENT_ACTIVE_COLUMN);
                Table tbl = TableUtil.getTableAtCell(activeSheet, startRow, startCol);
				boolean isSourceAsRange = true;
                if(tbl != null) {
					isSourceAsRange = false;
					JSONObjectWrapper resp = new JSONObjectWrapper();
					resp.put(JSONConstants.NAME_OF_RANGE, tbl.getName());
					responseMap.put("responseJson", resp);
                }
                else {
					for (Map.Entry<String, Range> stringRangeEntry : workbook.getNamedRanges().entrySet()) {
						if (stringRangeEntry.getValue().isMember(activeSheet, startRow, startCol)) {
							isSourceAsRange = false;
							JSONObjectWrapper resp = new JSONObjectWrapper();
							resp.put(JSONConstants.NAME_OF_RANGE, stringRangeEntry.getKey());
							responseMap.put("responseJson", resp);
						}
					}
				}

				if(isSourceAsRange)
				{
					DataRange dataRange = RangeUtil.expandPivotRange(activeSheet, startRow, startCol);
					JSONObjectWrapper expandedRange = new JSONObjectWrapper();
					expandedRange.put(JSONConstants.START_ROW, dataRange.getStartRowIndex());
					expandedRange.put(JSONConstants.START_COLUMN, dataRange.getStartColIndex());
					expandedRange.put(JSONConstants.END_ROW, dataRange.getEndRowIndex());
					expandedRange.put(JSONConstants.END_COLUMN, dataRange.getEndColIndex());
					responseMap.put("responseJson", expandedRange);
                }
                break;
            case ActionConstants.SOLVER:
                JSONObjectWrapper resJsonObj = ActionUtil.solve(workbook, startRow, startCol, endRow, endCol, actionJson.optString("ocsn", null), actionJson.optString("oc", null), actionJson.optString("val", null), actionJson.optString("vcsns", null), actionJson.optString("lhs", null), actionJson.optString("rhs", null), actionJson.optString("rel", null), actionJson.optString("cs", null), actionJson.optString("gt", null), actionJson.optBoolean("nnr", false), actionJson.optBoolean("vb", false));// No I18N
                responseMap.put("solveResponseJson", resJsonObj);
                break;
            case ActionConstants.FORMATCELLS_CLIENT:
            	JSONObjectWrapper jsonObj = FormatCellsUtils.getUserJsonObject(workbook, activeSheet, actionJson.getInt(JSONConstants.CURRENT_ACTIVE_ROW), actionJson.getInt(JSONConstants.CURRENT_ACTIVE_COLUMN),actionJson.getJSONObject("frmtjson"));// No I18N
            	responseMap.put("responseJson",jsonObj);
            	break;
            case ActionConstants.NUMBER_FORMAT_LIST:
                jsonObj = FormatCellsUtils.getNumberFormatLists(workbook);
                responseMap.put("responseJson",jsonObj);
                break;

			case ActionConstants.NUMBER_FORMAT_PREVIEW:
				String patternString = Utility.getDecodedString(actionJson.getString(JSONConstants.PATTERN_STRING));
				JSONObjectWrapper formatJSON = FormatCellsUtils.getPreviewForPatternString(workbook, activeSheet, patternString, startRow, startCol, actionJson.optBoolean(JSONConstants.IS_CUSTOM_PATTERN, false));
				jsonObj = new JSONObjectWrapper();
				jsonObj.put("format", formatJSON);
				responseMap.put("responseJson", jsonObj);
				break;

			case ActionConstants.CUSTOM_FORMAT_MANAGE:
				patternString = actionJson.optString(JSONConstants.PATTERN_STRING, null);
				if(patternString != null)
				{
					patternString = Utility.getDecodedString(patternString);
				}
				int deleteIndex = actionJson.optInt(JSONConstants.INDEX, -1);
				int formatAction = actionJson.getInt(JSONConstants.CUSTOM_FORMAT_ACTION);
				responseMap.put("responseJson", FormatCellsUtils.customFormatManage(workbook, patternString, deleteIndex, formatAction));
				break;
			case ActionConstants.GET_FORMULA_SUGGEST_INFO:
				Locale workBookLocale = CurrentRealm.getContainer().getWorkbookAdditionalInfo().getSpreadsheetLocale();
				responseMap.put(JSONConstants.FORMULA_SUGGEST_INFO, FunctionDescription.getFunctionDetails(workBookLocale));
				break;
//	case ActionConstants.PROTECT_RANGE_DIALOG:
//                boolean isSheet = actionJson.getBoolean("isSheet"); //NO I18N
//                boolean isNew = actionJson.getBoolean("isNew"); //NO I18N
//                boolean isExtend = actionJson.getBoolean("isExtend"); //NO I18N
//                ProtectedRange protectedRange = null;
//                if (isSheet == true) {
//                    protectedRange = sheet.getProtectedRange();
//                } else if (isExtend == true) {
//
//
//                    protectedRange = RangeUtil.getProtectedRange(sheet, actionJson.getInt("exSr"), actionJson.getInt("exSc"), actionJson.getInt("exEr"), actionJson.getInt("exEc")); //NO I18N
//                    if (protectedRange == null) {
//                        protectedRange = sheet.getProtectedRange();
//                    }
//                }
//
//                if (protectedRange == null) {
//                    protectedRange = RangeUtil.getProtectedRange(sheet, actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN), actionJson.getInt(JSONConstants.END_ROW), actionJson.getInt(JSONConstants.END_COLUMN));
//                    isNew = (protectedRange == null);
//                }
//            break;
			case ActionConstants.DATA_VALIDATION_READLIST:
				JSONObjectWrapper dvlist = DataValidationUtils.getListValues(activeSheet, startRow, startCol, actionJson.getInt("tparts"), actionJson.getInt("partnum")); // No I18N
				responseMap.put("dval", dvlist);  // No I18N
				break;
			case ActionConstants.DATA_VALIDATION_READSHEET:
				JSONArrayWrapper jarr = new JSONArrayWrapper();
				String dvsheet = DataValidationUtils.getDataValidationForSheet(activeSheet, jarr, actionJson.getString("dateFormat"), actionJson.getString("timeFormat"));// No I18N
				responseMap.put("dval", dvsheet);// No I18N

				break;


            	
	 		case ActionConstants.DATA_VALIDATION_READWORKBOOK:
	 			String dvwb = DataValidationUtils.getDataValidationForWorkbook(workbook, actionJson.getString("dateFormat"), actionJson.getString("timeFormat"));// No I18N
            	responseMap.put("dval", dvwb);// No I18N
	 			break;
	 		case ActionConstants.DATA_VALIDATION_READRANGE:
	 			String dvr = DataValidationUtils.getDataValidationForRange(actionJson.getString("dateFormat"), actionJson.getString("timeFormat"), listOfRanges);// No I18N
            	responseMap.put("dval", dvr);// No I18N
	 			break;
	 		case ActionConstants.VALIDATE_INPUT:
                        case ActionConstants.VALIDATE_INPUT_CF:
	 			String validateFor = actionJson.getString("validateFor");
	 			String valinp = "false";
	 			if(validateFor.equalsIgnoreCase("datavalidation")){
	 				valinp = DataValidationUtils.validateVals(activeSheet, actionJson.getString("valInpsJson"), startRow, startCol, endRow, endCol);// No I18N
 				}
                 else{//conditionalformat
	 			    // Converting old jsons to new json.
	 			    JSONObjectWrapper cfJSON = new JSONObjectWrapper(actionJson.getString("valInpsJson"));
	 			    if(!cfJSON.has(JSONConstants.RULES)){
	 			        int validateForCF;
	 			        if("colorscales".equals(validateFor)){
                            validateForCF = ActionConstants.COLOR_SCALES_APPLY;
                        }
	 			        else if("iconset".equals(validateFor)){
                            validateForCF = ActionConstants.ICON_SET_APPLY;
                        }
	 			        else{
                            validateForCF = ActionConstants.CONDITIONAL_FORMAT_APPLY;
                        }
	 			        cfJSON.put(JSONConstants.RULES, ThemeJsonConvertor.getCFNewModelJSON(cfJSON, validateForCF));
                    }
	 			    ///////
	 				valinp = ConditionFormatUtils.validateInputs(activeSheet, cfJSON,listOfRanges.stream().map(Range::toDataRange).collect(Collectors.toList()), validateFor);
	 			}
            	responseMap.put("valinp", valinp);// No I18N
	 			break;
	 		 case ActionConstants.DATA_HIGHLIGHT_INVALID:

	 		    	 if(wbcontainer!=null){
	 		    	 JSONObjectWrapper freezeInfoMap = DocumentUtils.getFreezePaneInfo(workbook, activeSheet.getName());
	 		    	 String tabType = actionJson.optString("tabtype"); //No I18N
	 		    	 if(uProfile!=null && freezeInfoMap!=null){
	 		    		  	ViewPort vp 		= null;
	 		    		  	String 	 response 	= null;
	 		    		  	if(tabType != null && tabType.equals("cached")) {
	 		    		  		//Constraints constraint = uProfile.getConstraints(CurrentRealm.getTabIdentity(), TabType.CACHED);
                                                        Constraints constraint = uProfile.getTabInfo(CurrentRealm.getTabIdentity(), CurrentRealm.getWmsIdentity(), TabType.CACHED).getConstraints();
	 		    		  		ArrayList<Area> areaList 	=	 constraint.getContemporaryViewPort().getActiveViewPortAreas(activeSheet.getAssociatedName());
	 		    		  		response = DataValidationUtils.validateSheetForInvalid(workbook, activeSheet.getAssociatedName(), areaList);
	 		    		  	} else {
	 		    		  		//vp= uProfile.getViewPort(CurrentRealm.getTabIdentity(),TabType.NON_CACHED);
                                                        vp= uProfile.getTabInfo(CurrentRealm.getTabIdentity(), CurrentRealm.getWmsIdentity(), TabType.NON_CACHED).getViewPort();
                                                        JSONObjectWrapper freezeRows = freezeInfoMap.has(JSONConstants.FREEZE_ROW_RANGE)?freezeInfoMap.getJSONObject(JSONConstants.FREEZE_ROW_RANGE) : null;
	 		    		  		int freezedRow= freezeRows != null ? Integer.parseInt(freezeRows.getString(JSONConstants.START_ROW)) : -1;
	 		    		  		response= DataValidationUtils.validateSheet(workbook, vp,freezedRow);
	 		    		  	}
//	 	    	      		workbook.setHighlightInvalidCell(true);
						wbcontainer.setHighlightInvalidCell(true);
						responseMap.put("invalidCells", response);

					}
				}

				break;
			case ActionConstants.GET_INVALID_CELLS:
				int offSetBit = actionJson.getInt("offSetBit"); // No I18N
				boolean isNeedData = actionJson.getBoolean("isNeedData"); // No I18N
				if(uProfile != null)
				{
					Constraints constraint = uProfile.getTabInfo(CurrentRealm.getTabIdentity(), CurrentRealm.getWmsIdentity(), TabType.CACHED).getConstraints();
					constraint.setCellMetaBit(offSetBit);
				}
				JSONArrayWrapper invalidCellArray = new JSONArrayWrapper();
				if(isNeedData)
				{
					for(Range range : listOfRanges)
					{
						RangeIterator rangeIterator = new RangeIterator(range, RangeIterator.IterationStartPositionEnum.TOP_LEFT, true, true, true, true, true, true);
						while(rangeIterator.hasNext())
						{
							ReadOnlyCell readOnlyCell = rangeIterator.next();
							if(readOnlyCell != null)
							{
								Cell currentCell = readOnlyCell.getCell();
								if(currentCell != null && !currentCell.isContentValid())
								{
									JSONArrayWrapper invalidCellRow = new JSONArrayWrapper();
									invalidCellRow.put(currentCell.getRowIndex());
									invalidCellRow.put(currentCell.getColumnIndex());
									invalidCellArray.put(invalidCellRow);
								}
							}
						}
					}
				}
				responseMap.put("invalidCells", (isNeedData) ? invalidCellArray : null);
				break;
			case ActionConstants.CHART_GET_OPTIONS:
				ChartEngineManager.getChartEngine(workbook).handleChartGetOption(activeSheet, actionJson, responseMap);
				break;
			case ActionConstants.RESET_HIGHLIGHT_INVALID:
				CurrentRealm.getContainer().setHighlightInvalidCell(false);
				break;
			case ActionConstants.FIND_TABLE_RANGE:
				TablePrediction tablePredicted = new TablePrediction(activeSheet, startRow, startCol, endRow, endCol);
				// if(!(startRow == endRow && startCol == endCol)){
				//     tablePredicted.applyPattern(new JSONObjectWrapper().put(TableConstraints.TABLE_PREDICTION_TYPE, "none"));
				// }
				tablePredicted.applyPattern(new JSONObjectWrapper().put(TableConstraints.TIME_OUT, 3000));
				JSONObjectWrapper tableDetls = tablePredicted.getTableRangeDetails();
				responseMap.put("tableRangeLabel", tableDetls);
				break;
			case ActionConstants.READ_DATA_DUPLICATES:
				TablePrediction table = new TablePrediction(activeSheet, startRow, startCol, endRow, endCol);
				if(!(startRow == endRow && startCol == endCol))
				{
					table.applyPattern(new JSONObjectWrapper().put(TableConstraints.TABLE_PREDICTION_TYPE, "none"));
				}
				String actionType = actionJson.getString("actionType");
				JSONObjectWrapper tableDetails = table.getTableRangeDetails();
				boolean frl = tableDetails.getBoolean(TableConstraints.IS_FIRST_ROW_AS_LABEL);
				Range tRange = table.getTableRange();
				startRow = (frl) ? tRange.getStartRowIndex() + 1 : tRange.getStartRowIndex();
				startCol = tRange.getStartColIndex();
				endRow = tRange.getEndRowIndex();
				endCol = tRange.getEndColIndex();
				endRow = (endRow == Utility.MAXNUMOFROWS - 1) ? activeSheet.getUsedRowIndex() : endRow;
				endCol = (endCol == Utility.MAXNUMOFCOLS - 1) ? activeSheet.getUsedColumnIndex() : endCol;
				tableDetails.put("tableRange", new Range(activeSheet, (frl) ? startRow - 1 : startRow, startCol, endRow, endCol).toString());
				Collection<Integer> columnArray = new ArrayList<>();
				for(int i = 0; i <= endCol - startCol; i++)
				{
					columnArray.add(i);
				}
				DataRange daraRange = new DataRange(activeSheet.getAssociatedName(), startRow, startCol, endRow, endCol);
				try
				{
					List<List<Integer>> duplicateMatrix = ActionUtil.getDuplicateRows(workbook, daraRange, columnArray, false, false, true, 1);
					boolean includeFirstOccurence = ("unique".equals(actionType)); // No I18N
					JSONArrayWrapper duplicateRanges = ActionUtil.getDuplicateRanges(workbook, activeSheet, startRow, startCol, endRow, endCol, duplicateMatrix, includeFirstOccurence, actionType);
					tableDetails.put("duplicatesInfo", ActionUtil.getDuplicatesInfo(workbook, activeSheet, duplicateMatrix, startRow, startCol, endRow, endCol, includeFirstOccurence, actionType));
					tableDetails.put("duplicateRecords", duplicateRanges);
					responseMap.put("tableRange", tableDetails);
				}
				catch(IllegalArgumentException e)
				{
					tableDetails.put("duplicatesInfo", "invalid");
					tableDetails.put("duplicateRecords", new JSONArrayWrapper());
					responseMap.put("tableRange", tableDetails);
				}


				break;
			case ActionConstants.READ_FREQUENCYY_TABLE:
				table = new TablePrediction(activeSheet, startRow, startCol, endRow, endCol);
				if(!(startRow == endRow && startCol == endCol))
				{
					table.applyPattern(new JSONObjectWrapper().put(TableConstraints.TABLE_PREDICTION_TYPE, "none"));
				}
				tableDetails = table.getTableRangeDetails();
				columnArray = new ArrayList<>();
				Range tblRange = table.getTableRange();
				if(actionJson.getString("findDataRange") != null)
				{
					if(actionJson.getBoolean("findDataRange") == true)
					{
						startRow = tblRange.getStartRowIndex();
						startCol = tblRange.getStartColIndex();
						endRow = tblRange.getEndRowIndex();
						endCol = tblRange.getEndColIndex();
						endRow = (endRow == Utility.MAXNUMOFROWS - 1) ? activeSheet.getUsedRowIndex() : endRow;
						endCol = (endCol == Utility.MAXNUMOFCOLS - 1) ? activeSheet.getUsedColumnIndex() : endCol;
						tableDetails.put("tableRange", new Range(activeSheet, startRow, startCol, endRow, endCol).toString());
					}
				}
				startRow = startRow + 1;
				columnArray.add(actionJson.getInt("colIndex") - startCol);  // No I18N
				daraRange = new DataRange(activeSheet.getAssociatedName(), startRow, startCol, endRow, endCol);
				List<List<Integer>> duplicateMatrix = ActionUtil.getDuplicateRows(workbook, daraRange, columnArray, true, false, true, 1);

				if(actionJson.getBoolean("findDataRange") == true)
				{
					tableDetails.put("headers", DataCleaningUtil.getColumnNames(activeSheet, new Range(activeSheet, startRow - 1, startCol, endRow, endCol)).toString());
				}
				JSONArrayWrapper columnDetails = ActionUtil.getColumnDetails(workbook, activeSheet, duplicateMatrix, startRow, startCol, endRow, endCol, actionJson.getInt("colIndex"));  // No I18N
				tableDetails.put("info", columnDetails);
				tableDetails.put("findDataRange", actionJson.getBoolean("findDataRange"));
				responseMap.put("duplicatesInfo", tableDetails);

				break;
			case ActionConstants.DATA_DUPLICATES_INFO:
				frl = actionJson.getBoolean("frl"); // No I18N
				startRow = (frl) ? startRow + 1 : startRow;
				boolean includeFirstOccurence = actionJson.getBoolean("includeFirstOccurence"); // No I18N
				boolean isCaseSensitive = actionJson.getBoolean("isCaseSensitive");  // No I18N
				boolean includeHiddenCells = actionJson.getBoolean("includeHiddenCells");// No I18N
				duplicateMatrix = ActionUtil.checkDuplicateOption(workbook, activeSheet, startRow, startCol, endRow, endCol, actionJson.getString("colomnArray"), isCaseSensitive, false, includeHiddenCells);

				JSONObjectWrapper duplicateInfo = new JSONObjectWrapper();
				actionType = actionJson.getString("actionType");
				duplicateInfo.put("duplicatesInfo", ActionUtil.getDuplicatesInfo(workbook, activeSheet, duplicateMatrix, startRow, startCol, endRow, endCol, includeFirstOccurence, actionType));
				duplicateInfo.put("duplicateRecords", ActionUtil.getDuplicateRanges(workbook, activeSheet, startRow, startCol, endRow, endCol, duplicateMatrix, includeFirstOccurence, actionType));
				responseMap.put("duplicatesInfo", duplicateInfo);
				break;
			case ActionConstants.DATA_CLEANSING:
				JSONObjectWrapper clusterInfo = new JSONObjectWrapper();
				Sheet sheetForDC = workbook.getSheetByAssociatedName(actionJson.optString("sheetName", null)); //No i18n
				int colForDC = actionJson.optInt("column", -1); //No i18n
				if(sheetForDC != null)
				{
					boolean findTableRange = false;
					Boolean findTableRangeOnly = actionJson.optBoolean("findTableRangeOnly"); //No i18n
					if(findTableRangeOnly != null && findTableRangeOnly == true)
					{
						findTableRange = true;
					}
					else
					{
						findTableRange = actionJson.optBoolean("findTableRange", false); //No i18n
					}
					int SR = -1, ER = -1;
					Range selectedTable = null;
					List<DataRange> ranges = ActionJsonUtil.getListOfDataRangesFromJsonObject(actionJson, false);
					if(ranges.get(0).isCell())
					{
						findTableRange = true;
					}


					if(findTableRange == true)
					{
						//convert range string to Range Object
						if(actionJson.has("range"))
						{
							List<Range> tableRange = ZiaManager.getRangesFromRangeString(sheetForDC, actionJson.getString("range"));
							selectedTable = tableRange.get(0);
							colForDC = selectedTable.getStartColIndex();
						}
						else
						{
							List<Range> tableRange = DataCleaningUtil.detectTable(listOfRanges);
							selectedTable = tableRange.get(0);
						}
						startRow = selectedTable.getStartRowIndex();
						startCol = selectedTable.getStartColIndex();
						endRow = selectedTable.getEndRowIndex();
						endCol = selectedTable.getEndColIndex();
						endRow = (endRow == Utility.MAXNUMOFROWS - 1) ? activeSheet.getUsedRowIndex() : endRow;
						endCol = (endCol == Utility.MAXNUMOFCOLS - 1) ? activeSheet.getUsedColumnIndex() : endCol;

						if(!selectedTable.isEmptyRange())
						{
							clusterInfo.put("tableRange", new JSONObjectWrapper().put("sr", startRow).put("sc", startCol).put("er", endRow)
									.put("ec", endCol).put("sn", sheetForDC.getAssociatedName()).put("headers", DataCleaningUtil.getColumnNames(sheetForDC, selectedTable).toString()));
							SR = selectedTable.getStartRowIndex();
							ER = selectedTable.getEndRowIndex();
						}


					}
					else
					{
						SR = ranges.get(0).getStartRowIndex();
						ER = ranges.get(0).getEndRowIndex();
					}
					if(!findTableRangeOnly && SR != -1 && ER != -1)
					{
						JSONArrayWrapper result = null;
						try
						{
							result = DataCleaningUtil.getClusters(sheetForDC, SR, ER, colForDC);
						}
						catch(Exception e)
						{
							result = null;
						}
						if(result != null)
						{
							clusterInfo.put("values", result.toString());
						}
						else
						{
							clusterInfo.put("values", new JSONArrayWrapper());
						}
					}
					clusterInfo.put("col", colForDC);
				}
				responseMap.put("clusters", clusterInfo);

				break;
			case ActionConstants.JSP_VERSION_VIEW_DIALOG:
				UserProfile userProfile = CurrentRealm.getUserProfile();
				zuid = userProfile.getZUserId();
				boolean isParseError = actionJson.has("isParseError") ? actionJson.getBoolean("isParseError") : false;// no i18n
				boolean isNamedVersionOnly = actionJson.has("isNamedVersionOnly") ? actionJson.getBoolean("isNamedVersionOnly") : false;// no i18n
				int index = actionJson.has("index") ? actionJson.getInt("index") : 0;// no i18n
				jsonObj = actionJson.has("job") ? actionJson.getJSONObject("job") : null;// no i18n
				double startVersionNo = actionJson.has("startVersionNo") ? actionJson.getDouble("startVersionNo") : 0;// no i18n
				double scrollHeight = actionJson.has("scrollHeight") ? actionJson.getDouble("scrollHeight") : 0;// no i18n
				JSONObjectWrapper allVersionInfo = AuditTrailHelper.getAllVersionsInfo(CurrentRealm.getContainer(), isParseError, index, startVersionNo, scrollHeight, isNamedVersionOnly, jsonObj);
				// System.out.println("allVersionInfo : " + allVersionInfo);

				//CollaborationApi.deleteMemberSession(zuid, CurrentRealm.getContainer().getCollabId(), zuid, CurrentRealm.getTabIdentity());
				// -- Refreshing collaboration Sessions -- //
				//TODO: Need to check the below line
				//DocumentUtils.refreshWMSUserSessions(CurrentRealm.getContainer().getCollabId(), "", "refresh", null, false); //NO I18N

                responseMap.put("vview", allVersionInfo);
                break;
                
            case ActionConstants.GET_VERSION_DETAILS:
                boolean isNamedVersion = actionJson.has("isNamedVesionOnly") ? actionJson.getBoolean("isNamedVesionOnly") : false;// no i18n
                boolean isActionListRequired = actionJson.has("isActionListRequired") ? actionJson.getBoolean("isActionListRequired") : false; // NO I18N
                int startFrom = actionJson.getInt("startFrom"); // NO I18N
                boolean totalCountNeeded = actionJson.getBoolean("totalCountNeeded"); // NO I18N
                responseMap = AuditTrailHelper.getVersionDetails(wbcontainer, isNamedVersion, startFrom);
                if(isActionListRequired){
                    responseMap.put("lists", ActionUtil.getListsForAuditTrail(wbcontainer, false, true,""));
                }
                if(totalCountNeeded) {
                	responseMap.put(JSONConstants.TOTAL_VERSIONS, AuditTrailHelper.getTotalVersionsCount(wbcontainer));
                }
                break;
            case ActionConstants.FORM_PUBLISH:
                ActionUtil.publishForm(wbcontainer, workbook,  actionJson);
                break;
            case ActionConstants.FORM_UNPUBLISH:
                ActionUtil.unpublishForm(wbcontainer, workbook,  actionJson);
                break;
			case ActionConstants.FETCH_NAV_OBJECTS_DETAILS: {
				List<NavObject> navObjects = NavUserActionHandler.fetchNavObjects(new NavUserActionDataHolder(workbook));
				responseMap.put(NavUserActionConstant.NAV_OBJ_DETAILS, NavUserActionResponseGenerator.generateResponseForFetchNavObjects(workbook, navObjects));
				break;
			}
			case ActionConstants.CHART_META_LIST:
				ChartEngineManager.getChartEngine(workbook).handleChartMetaList(workbook, actionJson, responseMap);
				break;
			case ActionConstants.RECOMMEND_CHARTS: {
				ChartEngineManager.getChartEngine(workbook).handleRecommendCharts(wbcontainer, activeSheet, actionJson, responseMap);
				break;
			}
			case ActionConstants.COPY_CHART_STYLES: {
				ChartEngineManager.getChartEngine(workbook).handleCopyChartStyles(activeSheet, actionJson, responseMap);
				break;
			}
			case ActionConstants.COPY_CHART: {
				ChartEngineManager.getChartEngine(workbook).handleCopyChart(wbcontainer, activeSheet, actionJson, responseMap);
				break;
			}
			case ActionConstants.CLEAR_CHART_RECOMMENDATION_CACHE: {
				ChartEngineManager.getChartEngine(workbook).handleClearChartRecommendationCache(wbcontainer, activeSheet, actionJson);
				break;
			}
    		case ActionConstants.CHART_SAVE:
				ChartEngineManager.getChartEngine(workbook).handleChartSave(wbcontainer, activeSheet, actionJson);
				break;
			case ActionConstants.CHART_UPDATE:
				ChartEngineManager.getChartEngine(workbook).handleChartUpdate(wbcontainer, activeSheet, actionJson);
				break;
			case ActionConstants.CHART_DETAILS:
				ChartEngineManager.getChartEngine(workbook).handleChartDetails(activeSheet,
						tabTypeObj, actionJson, listOfRanges, startRow, startCol, endRow, endCol, responseMap);
				break;
			case ActionConstants.ADD_CHART_TO_IMAGE_LIBRARY:
				ChartEngineManager.getChartEngine(workbook).handleAddChartToImageLibrary(activeSheet, actionJson, responseMap);
				break;
			case ActionConstants.SERVERCLIP_COPY_CHART:
				ChartEngineManager.getChartEngine(workbook).handleServerClipCopyChart(wbcontainer, activeSheet, actionJson);
				return null;
			case ActionConstants.OPEN_EXPLORE:
				NLInteractorImpl n = new NLInteractorImpl();

				responseMap.put("recList", n.getRecommendedQueries(workbook, activeSheet, actionJson.getInt("sr"), actionJson.getInt("sc"), actionJson.getInt("er"), actionJson.getInt("ec")).toArray());
				break;

			case ActionConstants.PROCESS_SPELL_CHECK:
				responseMap.put(String.valueOf(ActionConstants.PROCESS_SPELL_CHECK), (new SpellCheckUtil(wbcontainer, workbook)).performSpellCheckAction(activeSheet, actionJson, true));
				break;
			case ActionConstants.DO_ATD_ACTION:
				responseMap.put(SpellCheckUtil.ATD_RESPONSE_STR, (new SpellCheckUtil(wbcontainer, workbook)).performATDAction(actionJson.optJSONObject(SpellCheckUtil.ATD_ACTION_JSON_STR)));
				break;
			case ActionConstants.DETECT_TABLE:
				responseMap.put(JSONConstants.DETECT_TABLE_DETAILS , ZiaManager.getTable(activeSheet,listOfRanges));
				break;
			case ActionConstants.GET_CHART_RECOMMENDATION:
				ChartEngineManager.getChartEngine(workbook).handleGetChartRecommendation(activeSheet, tabTypeObj, actionJson, responseMap);
				break;
			case ActionConstants.Z_TRANSLATION:
				responseMap.put(JSONConstants.Z_TRANSLATION_RESP, SheetTranslationUtils.translateSentence(workbook, actionJson.getJSONObject(JSONConstants.Z_TRANSLATION_PARAMS)));
				break;
			case ActionConstants.Z_TRANSLATION_GET_DATA:
				responseMap.put(JSONConstants.Z_TRANSLATION_RESP, SheetTranslationUtils.getSourceData(workbook, actionJson.getJSONObject(JSONConstants.Z_TRANSLATION_PARAMS)));
				break;
            case ActionConstants.ZIA_2:
                actionJson.getJSONObject(JSONConstants.ZIA_PARAMS).put(JSONConstants.CURRENT_ACTIVE_CELL,new JSONArrayWrapper().put(startRow).put(startCol).put(endRow).put(endCol));
                responseMap.put(JSONConstants.ZIA_2_RESP, ZSZiaUtilities.analyseZia(activeSheet,actionJson));
                break;
			case ActionConstants.COLUMN_STATS:
				JSONObjectWrapper reqParams = actionJson.getJSONObject(JSONConstants.COLUMN_STATS_PARAMS);
				reqParams.put(JSONConstants.CURRENT_ACTIVE_CELL, new JSONArrayWrapper().put(startRow).put(startCol).put(endRow).put(endCol));
				responseMap.put(JSONConstants.COLUMN_STATS_RESP, CSAction.executeAction(activeSheet, reqParams));
				break;
			case ActionConstants.PIVOT_CHART_DETAILS:
				 ChartEngineManager.getChartEngine(workbook).handlePivotChartDetails(activeSheet, actionJson, responseMap);
				 break;		
			 case ActionConstants.CHART_REFRESH:
				 ChartEngineManager.getChartEngine(workbook).handleChartRefresh(workbook, activeSheet, actionJson, responseMap);
				 break;
			case ActionConstants.FETCH_DOCUMENTSETTINGS:
				responseMap.put("responseJson", SpreadsheetSettingsUtil.fetchSpreadsheetSettings_Old(wbcontainer, actionJson.getString(JSONConstants.CURRENT_USER_LOCALE), actionJson.getBoolean(JSONConstants.LOCALE_LIST_UPDATED)));
				break;
			case ActionConstants.FETCH_DOCUMENT_SETTINGS_NEW:
				responseMap.put("responseJson", SpreadsheetSettingsUtil.fetchSpreadsheetSettings(wbcontainer));
				break;
			case ActionConstants.FETCH_CUSTOM_COLORS:
				try
				{
					responseMap.put(JSONConstants.CUSTOM_COLORS, wbcontainer.getCustomColors());
					responseMap.put(DataAPIConstants.STATUS, DataAPIConstants.SUCCESS);
				}
				catch(Exception e)
				{
					throw e;
				}
				break;
			case ActionConstants.UPDATE_CUSTOM_COLORS:
				try
				{
					JSONArrayWrapper customColors = actionJson.getJSONArray(JSONConstants.CUSTOM_COLORS);
					wbcontainer.updateCustomColors(customColors);
					responseMap.put(DataAPIConstants.STATUS, DataAPIConstants.SUCCESS);
				}
				catch(Exception e)
				{
					throw e;
				}
				break;
			case ActionConstants.CHART_PREVIEW:
				ChartEngineManager.getChartEngine(workbook).handleChartPreview(activeSheet, actionJson, responseMap);
				break;
			case ActionConstants.FETCH_DECLARATIVE_TABLE:
				ChartEngineManager.getChartEngine(workbook).handleFetchDeclarativeTable(activeSheet, actionJson, responseMap);
				break;
			case ActionConstants.CLEAR_CHART_DETAILS:
				ChartEngineManager.getChartEngine(workbook).handleClearChartDetails(responseMap);
				break;
			case ActionConstants.CHART_CELL_REF:
				ChartEngineManager.getChartEngine(workbook).handleChartCellRef(workbook, actionJson, responseMap);
				break;
			case ActionConstants.VIRTUAL_CHART:
				ChartEngineManager.getChartEngine(workbook).handleVirtualCharts(wbcontainer, workbook, actionJson, responseMap);
				break;
			case ActionConstants.PIVOT_CHART_BUTTON_DETAILS:
				ChartEngineManager.getChartEngine(workbook).handlePivotChartButtonDetails(workbook, actionJson, responseMap);
				break;
			case ActionConstants.CHART_COPY_STYLE:
				ChartEngineManager.getChartEngine(workbook)
						.handleChartsStyleCopy(workbook, activeSheet, actionJson);
				break;
//                        case ActionConstants.CHART_COPY_CLIP:
//                                        responseMap.put("csc",ChartUtils.setChartServerClip(workbook, actionJson));//csc- chart server clip
//                                        break;
//                        case ActionConstants.CHART_REMOVE_CLIP:
//                                        ChartUtils.removeChartClip(actionJson);
//                                        break;
//                        case ActionConstants.CHART_QUICK_STYLE:
//                                    JSONObjectWrapper styleDetail = new JSONObjectWrapper();
//                                    styleDetail.put("cco",ChartUtils.doQuickStyleAction(workbook,sheet,actionJson));
//                                    responseMap.put("cso", styleDetail); // cso --- chartStyleObject response
//                                    break;

//            case ActionConstants.READ_RANGE_STYLES:
//                responseMap.put("responseJson", ActionUtil.getRangeFormat(sheet, actionJson.getInt("sr"), actionJson.getInt("sc"), actionJson.getInt("er"), actionJson.getInt("ec"), null));
//                // Clear InclusiveParentStyle created in each CellStyle.
//                workbook.clearInClusiveParentCellStyle();
//                break;
			case ActionConstants.READ_PIVOT_FILTER_DATA:
				int appliedGroup = actionJson.has(JSONConstants.APPLIED_GROUP) ? actionJson.getInt(JSONConstants.APPLIED_GROUP) : -2;
				responseMap.put("responseJson", ActionUtil.getPivotFilterData(workbook, actionJson.getString(JSONConstants.ID), actionJson.getInt(JSONConstants.START_ROW), actionJson.getInt(JSONConstants.START_COLUMN),appliedGroup));
				break;
			case ActionConstants.CELLSTYLES_DEFINITION:
				JSONArrayWrapper cellStyleNameAry = new JSONArrayWrapper(actionJson.getString(JSONConstants.CELLSTYLES_NAME));
				JSONObjectWrapper cellStyleDefJson = com.adventnet.zoho.websheet.model.response.v1.util.ResponseUtils.getCellStylesDefinitionObj(workbook, cellStyleNameAry, false);
				responseMap.put(JSONConstants.CELLSTYLES_DEFINITION, cellStyleDefJson);
				break;
			case ActionConstants.RESET_ANNOTE_POSITION:
				ActionUtil.resetAnnotationPosition(activeSheet, startRow, startCol, actionJson.optString("x", null), actionJson.optString("y", null), actionJson.optString("wd", null), actionJson.optString("ht", null));  // No I18N
				break;
			case ActionConstants.DRE_CREATE:
				responseMap.put(JSONConstants.DELUGE_RESPONSE, DelugeUtils.createFunction(wbcontainer, workbook, actionJson, true));
				break;
			case ActionConstants.DRE_CREATEANDEXECUTE:
				responseMap.put(JSONConstants.DELUGE_RESPONSE, DelugeUtils.createAndExecuteFunction(actionJson, workbook));
				break;
			case ActionConstants.DRE_UPDATE:
				responseMap.put(JSONConstants.DELUGE_RESPONSE, DelugeUtils.updateFunction(actionJson, workbook, true));
				break;
			case ActionConstants.DRE_UPDATEANDEXECUTE:
				responseMap.put(JSONConstants.DELUGE_RESPONSE, DelugeUtils.updateAndExecuteFunction(actionJson, workbook));
				break;
//	case ActionConstants.DRE_GETALL:
//		responseMap.put(JSONConstants.DELUGE_RESPONSE,DelugeUtils.getAllFunctionDetails(actionJson, workbook,true));
//		break;
			case ActionConstants.DRE_GETDETAIL:
				JSONObjectWrapper resp = DelugeUtils.getFunctionDetails(actionJson, workbook, true);
				responseMap.put(JSONConstants.DELUGE_RESPONSE, resp);
				break;
			case ActionConstants.DRE_EXECUTE:
				responseMap.put(JSONConstants.DELUGE_RESPONSE, DelugeUtils.executeFunction(actionJson, workbook));
				break;
			case ActionConstants.DRE_DELETE:
				responseMap.put(JSONConstants.DELUGE_RESPONSE, DelugeUtils.deleteFunction(actionJson, workbook, true));

		break;
	case ActionConstants.DRE_GETNAME:
		responseMap.put(JSONConstants.DELUGE_RESPONSE,DelugeUtils.getAllFunctionName(actionJson, workbook));

		break;
	case ActionConstants.DRE_CONNECTIONS:
		responseMap.put(JSONConstants.DELUGE_RESPONSE,DelugeUtils.validateUser(workbook));

		break;
	case ActionConstants.DRIVE_STATUS:
		boolean success = ZohoFS.markDraftasComplete(wbcontainer.getDocsSpaceId(), wbcontainer.getResourceId(), actionJson.getString("zuid") );
		responseMap.put("DRIVE_STATUS", success);
		break;
	case ActionConstants.WORKFLOW_META:
		responseMap.put("WORKFLOW_META", WorkDriveInfoUtils.getWorkflowInfo(wbcontainer.getResourceId()).toString());
		break;
	case ActionConstants.FETCH_WORKFLOW_LIST:
		if(!wbcontainer.isRemoteMode()) {
			responseMap.put("FETCH_WORKFLOW_LIST", WorkDriveInfoUtils.fetchAllManualWorkflows(wbcontainer.getResourceId()).toString());
		}
		break;
	case ActionConstants.FETCH_WORKFLOW_STATUS:
		String orgId = actionJson.getString("orgId");
		String instanceId = actionJson.getString("instanceId");
		responseMap.put("WORKFLOW_STATUS", WorkDriveInfoUtils.getWorkflowStatus(orgId, instanceId).toString());
		break;
	case ActionConstants.DOCUMENT_PROPERTIES:
		LOGGER.info("DOCUMENT_PROPERTIES::");
		responseMap.put("DOCUMENT_PROPERTIES", DocumentUtils.getDocumentProperties(wbcontainer.getResourceId()));
		break;
    case ActionConstants.PICKLIST_INTERSECTION:
        responseMap.put("picklist", PicklistUtil.getPicklistsInSelectedRange(workbook,listOfRanges,activeSheet));
        break;
    case ActionConstants.PICKLIST_MANAGE:
        responseMap.put("picklists", PicklistUtil.getPicklistsForManage(workbook));
        break;
	case ActionConstants.PICKLIST_RANGES:
		responseMap.put("picklist", PicklistUtil.getPicklistRanges(workbook, actionJson.getInt(JSONConstants.PICKLIST_ID)));
		break;
    case ActionConstants.PICKLIST_READ:
        Set<String> uniqueList = new LinkedHashSet<>();
        boolean exceeded = PicklistUtil.getUniqueValues(listOfRanges,uniqueList);
        responseMap.put("values", uniqueList);
        responseMap.put("isLimitExceeded",exceeded);
        break;

            case ActionConstants.PREDICT_TABLE_RANGE:
                Range tableRange;
                boolean isTablePresent = false;
                boolean isRangeExpand = actionJson.optBoolean(JSONConstants.IS_RANGE_EXPAND, false);
                if(!isRangeExpand && !(RangeUtil.isInMergedArea(activeSheet, startRow, startCol, endRow, endCol)) && (endRow - startRow > 0 || endCol - startCol > 0)) {
                    tableRange = new Range(activeSheet, startRow, startCol, endRow, endCol);
                }
                else {
					if(isRangeExpand)
					{
						endRow = Math.min(endRow, activeSheet.getUsedRowIndex());
						endCol = Math.min(endCol, activeSheet.getUsedColumnIndex());
					}
					TablePrediction tablePrediction = new TablePrediction(activeSheet, startRow, startCol, endRow, endCol);
	                tablePrediction.applyPattern(new JSONObjectWrapper().put(TableConstraints.TIME_OUT, 3000));
                    tableRange = tablePrediction.getTableRange();
	                if(tableRange == null)
	                {
		                LOGGER.log(Level.SEVERE, "[TABLE_PREDICTION][Exception] Table Prediction has taken more than 3 seconds");
		                throw new ProhibitedActionException(ErrorCode.ERROR_INTERNAL_SERVER);
	                }
					if(actionJson.optBoolean(JSONConstants.TABLE_PREDICTION_IS_FILTER_MERGED_HEADERS, false) && !tablePrediction.isIntersectsTable()) {
						tableRange = tablePrediction.doFilterRange(tableRange, "COLS"); //NO I18N
					}
					isTablePresent = tablePrediction.isIntersectsTable();
                }
                boolean isShowHeader = TableUtil.predictHeaderBoolean(tableRange);
                responseMap.put(JSONConstants.CONTAINS_HEADER, isShowHeader);
                responseMap.put(JSONConstants.IS_TABLE_PRESENT, isTablePresent);
            responseMap.put("range", new JSONObjectWrapper().put(JSONConstants.START_ROW, tableRange.getStartRowIndex()).put(JSONConstants.START_COLUMN, tableRange.getStartColIndex())
            .put(JSONConstants.END_ROW, tableRange.getEndRowIndex()).put(JSONConstants.END_COLUMN, tableRange.getEndColIndex()).put(JSONConstants.ASSOCIATED_SHEET_NAME, tableRange.getSheet().getAssociatedName()));
//                responseMap.put("range", tableRange.getRangeStringForClient());
				break;

			case ActionConstants.TABLE_CREATE_CHECK:
				try
				{
					TableUtil.isTableCreationPossible(workbook, listOfRanges.get(0).toDataRange(), exSharedLinkId);
					responseMap.put(JSONConstants.CREATE, true);
				}
				catch(Exception e)
				{
					responseMap.put(JSONConstants.CREATE, false);
					responseMap.put(JSONConstants.ERROR_MESSAGE, e.getMessage());
				}
				break;
    case ActionConstants.SPILL_OBSTRUCTING_CELLS:
        List<DataRange> obstructingCells = ActionUtil.getObstructingCells(activeSheet, actionJson.getInt(JSONConstants.CURRENT_ACTIVE_ROW), actionJson.getInt(JSONConstants.CURRENT_ACTIVE_COLUMN));
        JSONArrayWrapper ranges = new JSONArrayWrapper();
        for(DataRange range: obstructingCells) {
            JSONArrayWrapper array = new JSONArrayWrapper();
            array.put(range.getStartRowIndex()).put(range.getStartColIndex()).put(range.getEndRowIndex()).put(range.getEndColIndex());
            ranges.put(array);
        }
        responseMap.put(JSONConstants.RANGES, ranges);
        break;
    case ActionConstants.MARK_AS_FAVOURITE:
        
        JSONObjectWrapper response = new JSONObjectWrapper();
        boolean isFavorite = actionJson.getBoolean("favorite");
        String userID = actionJson.getString(JSONConstants.ZUID);
        String rid = actionJson.getString("rid");
        
        boolean fav_status = false;
        
        if(isFavorite){
            fav_status = ZohoFS.markAsFavourite(String.valueOf(userID), rid);
        } else {
            fav_status = ZohoFS.removeFavourite(String.valueOf(userID), rid);
        }
        response.put("isFavorite", isFavorite);
        response.put("status", fav_status);
        responseMap.put(DataAPIConstants.KEY_RESPONSE, response);
        break;
    case ActionConstants.SEND_ACCOUNT_CONFIRMATION_MAIL:
    	try {
    		response = new JSONObjectWrapper();
            String remoteAddress = actionJson.getString("remoteAddress");
    		if(UserInfo.sendConfirmationMail(remoteAddress)) {
    			response.put("status", "success");
    		}else {
    			response.put("status", "failure");
    		}    
            responseMap.put(DataAPIConstants.KEY_RESPONSE, response);
    	}catch(Exception e) {
    		
    	}
        
        break;
      
    case ActionConstants.CHECK_ACCOUNT_CONFIRMATION_STATUS:
    	response = new JSONObjectWrapper();
   
        boolean isUnconfirmedUser = UserInfo.isUnconfirmedUser();
        
		response.put("isUnconfirmedUser", isUnconfirmedUser);
        responseMap.put(DataAPIConstants.KEY_RESPONSE, response);
        break;
		
			case ActionConstants.LINKED_WORKBOOKS:

				JSONObjectWrapper linkedWorkbookDetails = new JSONObjectWrapper();
				responseMap.put(DataAPIConstants.KEY_RESPONSE, linkedWorkbookDetails);

				List<Webhook> webhooks = workbook.getWebhooks();
				if(webhooks == null)
				{
					webhooks = new ArrayList<>();
				}

				JSONArrayWrapper consumerWorkbooksDetails = new JSONArrayWrapper();
				linkedWorkbookDetails.put(ExternalRangeUtils.KEY_CONSUMERS, consumerWorkbooksDetails);

				JSONArrayWrapper supplierWorkbooksDetails = new JSONArrayWrapper();
				linkedWorkbookDetails.put(ExternalRangeUtils.KEY_SUPPLIERS, supplierWorkbooksDetails);

				for(String supplierWorkbookRSID : wbcontainer.getExternalRangeManager().getExternalRangeCache().getSuppliers().keySet())
				{
					if(WorkbookLink.isResourceActive(supplierWorkbookRSID, true))
					{
						ResourceInfo resourceInfo = ZohoFS.getResourceInfo(supplierWorkbookRSID);
						String documentName = resourceInfo.getName();
						boolean isUserHasReadAccessToSupplier = AuthorizationUtil.isUserHasAccessForDoc(ShareConstants.PERMISSION_READ, actionJson.getString(JSONConstants.ZUID), supplierWorkbookRSID, true);

						JSONObjectWrapper jsonObject = new JSONObjectWrapper();
						supplierWorkbooksDetails.put(jsonObject);
						jsonObject.put(JSONConstants.ID, supplierWorkbookRSID);
						jsonObject.put(JSONConstants.DOC_NAME, documentName);
						jsonObject.put(ExternalRangeUtils.KEY_ACCESS, isUserHasReadAccessToSupplier ? 1 : 0);
					}
				}
				break;
			case ActionConstants.GET_DATA_FROM_TABLE:

				String cellContent = actionJson.getString("v");
				JSONArrayWrapper _htmlDataArray = (new HtmlToJSON(cellContent, 0, 0).parse());
				ArrayList<ArrayList> outerArrayList = new ArrayList<ArrayList>();
				for(int i = 0; i < _htmlDataArray.length(); i++)
				{
					JSONArrayWrapper asns = _htmlDataArray.getJSONArray(i);
					ArrayList<String> innerArrayList = new ArrayList<String>();
					for(int j = 0; j < asns.length(); j++)
					{
						JSONObjectWrapper range1 = asns.getJSONObject(j);
						if(range1.has("style"))
						{
							JSONObjectWrapper style = (JSONObjectWrapper) range1.get("style");
							if(style.has("display") && style.get("display").equals("none"))
							{
								continue;
							}
						}
						if(range1.has("data"))
						{
							String tempString = URLDecoder.decode(((range1.getString("data"))), HtmlParserConstant.CHARTSET);
							tempString = tempString.trim();
							innerArrayList.add(j, URLEncoder.encode(tempString, HtmlParserConstant.CHARTSET));
						}
						else
						{
							innerArrayList.add(j, "");
						}
					}
					if(!innerArrayList.isEmpty())
					{
						outerArrayList.add(innerArrayList);
					}
				}
				responseMap.put(JSONConstants.TABLE_DATA, outerArrayList.toString());

				break;
			case ActionConstants.FETCH_CELL_EDIT_HISTORY:
				try
				{
					JSONObjectWrapper range = actionJson.getJSONObject(JSONConstants.RANGELIST);
					startRow = range.getInt(JSONConstants.START_ROW);
					startCol = range.getInt(JSONConstants.START_COLUMN);
					endRow = range.getInt(JSONConstants.END_ROW);
					endCol = range.getInt(JSONConstants.END_COLUMN);
					SheetRange requestedRange = new SheetRange(startRow, startCol, endRow, endCol);
					double versionNo = actionJson.getDouble(JSONConstants.CELL_EDIT_HISTORY_VERSION_NO);
					index = actionJson.getInt(JSONConstants.INDEX);
					int pIndex = actionJson.getInt(JSONConstants.PARENT_INDEX);
//					int limit = Math.min(actionJson.optInt(JSONConstants.LIMIT, CELL_EDIT_HISTORY_LIMIT), CELL_EDIT_HISTORY_LIMIT);
					responseMap = (HashMap<String, Object>) CellEditHistoryUtil.getCellEditHistory(wbcontainer, activeSheet, requestedRange, versionNo, pIndex, index);
				}
				catch(Exception e)
				{
					LOGGER.log(Level.SEVERE, "[CELL_EDIT_HISTORY][Exception] Exception while retrieving cell edit history", e);
					throw new Exception(ErrorCode.ERROR_CELL_EDIT_HISTORY);
				}
				break;

			case ActionConstants.STORE_THUMBNAIL:
				if(DocumentUtils.checkClientThumbnailHoldOnTime(wbcontainer)){
					String dataURL = actionJson.getString("dataURL");
					String imageString = dataURL.substring(dataURL.indexOf(",") + 1);
					EngineUtils1.storeThumbnailFromClient(imageString, wbcontainer);
					MessagePropagator.sendThumbnailResponse(wbcontainer);
					responseMap.put("STATUS", "success" );
				}
				break;

			case ActionConstants.FETCH_USER_SPREADSHEET_SETTINGS:
				try
				{
					responseMap.put(JSONConstants.USER_SPREADSHEET_SETTINGS, UserSpreadSheetSettings.getUserSettings(user));
					responseMap.put(JSONConstants.THEMES, ZSThemesLibrary.readZSThemes());
					responseMap.put(DataAPIConstants.STATUS, DataAPIConstants.SUCCESS);
				}
				catch(Exception e)
				{
					LOGGER.log(Level.SEVERE, "[USER_SETTINGS][Exception] Exception while retrieving user settings for the user "+ user.getZuid(), e);
					throw new Exception(ERROR_USER_SETTINGS_FETCH);
				}
				break;
			case ActionConstants.SAVE_USER_SPREADSHEET_SETTINGS:
				try
				{
					UserSpreadSheetSettings.saveUserSettings(user, actionJson.getJSONObject(JSONConstants.USER_SPREADSHEET_SETTINGS));
					responseMap.put(DataAPIConstants.STATUS, DataAPIConstants.SUCCESS);
				}
				catch(Exception e)
				{
					LOGGER.log(Level.SEVERE, "[USER_SETTINGS][Exception] Exception while saving user settings for the user "+ user.getZuid(), e);
					throw new Exception(ERROR_USER_SETTINGS_SAVE);
				}
				break;

			default:
				break;
		}

		long timeStamp2 = System.currentTimeMillis();
		long actionTime = (timeStamp - timeStamp2);
		LOGGER.log(Level.INFO, "RESOURCE_ID: {0} >>> Total Time taken to Execute user Action, ActionConstant :: {1} ::::: {2} ms" ,
				new Object[] {
						wbcontainer.getResourceKey(), //RESOURCE_ID: {0}
						actionJson.has("a") ? actionJson.getInt("a") : null, //ActionConstant :: {1}
						actionTime, //::::: {2}
				});
		return responseMap;
	}

	private static JSONArrayWrapper addExpandSelectionDetails(Sheet sheet, int startRow, int startCol, int endRow, int endCol)
	{
		JSONArrayWrapper jsonArray = new JSONArrayWrapper();
		boolean hasContent;
		Cell cell;
		int sC = startCol, eC = endCol;

		for(int i = startCol - 1; i >= 0; i--)
		{
			hasContent = false;
			for(int j = startRow; j <= endRow; j++)
			{
				cell = sheet.getReadOnlyCellFromShell(j, i).getCell();
				if(cell != null && (cell.getValue().getType() != Type.UNDEFINED))
				{
					hasContent = true;
					sC = i;
					break;
				}
			}
			if(!hasContent)
			{
				break;
			}
		}

		int usedCol = sheet.getUsedColumnIndex();
		for(int i = endCol; i <= usedCol; i++)
		{
			hasContent = false;
			for(int j = startRow; j <= endRow; j++)
			{
				cell = sheet.getReadOnlyCellFromShell(j, i).getCell();
				if(cell != null && (cell.getValue().getType() != Type.UNDEFINED))
				{
					hasContent = true;
					eC = i;
					break;
				}
			}

			if(!hasContent)
			{
				break;
			}
		}

		JSONObjectWrapper expandedRangeObj = checkForContainsHeader(sheet, startRow, sC, endRow, eC);
		if(startCol != sC || endCol != eC)
		{
			expandedRangeObj.put("ESC", sC);
			expandedRangeObj.put("EEC", eC);
			expandedRangeObj.put("ES", "true");
		}
		jsonArray.put(expandedRangeObj);
		jsonArray.put(checkForContainsHeader(sheet, startRow, startCol, endRow, endCol));
		return jsonArray;
	}

	private static JSONObjectWrapper checkForContainsHeader(Sheet sheet, int startRow, int startCol, int endRow, int endCol)
	{
		JSONObjectWrapper jsonObj = new JSONObjectWrapper();
		Cell headerCell = null, dataCell = null;
		int tempEndRow = (startRow + 2) <= endRow ? (startRow + 2) : endRow;
		boolean isHeader = false, containsHeader = false;
		String content;
		for(int colIndex = startCol; colIndex <= endCol; colIndex++)
		{
			headerCell = sheet.getCell(startRow, colIndex);
			content = headerCell.getContent();
			if(content == null || "".equals(content))
			{
				content = "Column " + CellUtil.getColumnReference(colIndex);   //No I18N
			}

			jsonObj.put("c_".concat(String.valueOf(colIndex)), content);

			if(headerCell.getType() == Type.UNDEFINED)
			{
				continue;
			}

			for(int rowIndex = startRow + 1; rowIndex <= tempEndRow; rowIndex++)
			{
				dataCell = sheet.getCell(rowIndex, colIndex);

				if(dataCell.getType() == Type.UNDEFINED)
				{
					continue;
				}

				if(headerCell.getType() == dataCell.getType())
				{
					isHeader = false;
					break;
				}
				else
				{
					isHeader = true;
				}
			}
			if(isHeader)
			{
				containsHeader = true;
			}
		}
		jsonObj.put("ch", String.valueOf(containsHeader));
		return jsonObj;
	}

	// add expire time to serverclip object
	private static JSONObjectWrapper getServerClip(JSONObjectWrapper serverclipObject)
	{
		JSONObjectWrapper msgJson = new JSONObjectWrapper();
		if(serverclipObject == null)
		{
			msgJson.put(JSONConstants.COPY_CLIP, false);
		}
		else
		{
			int expireTime = ActionUtil.getClipTimeToLive();
			serverclipObject.put(JSONConstants.COPY_CLIP_TTL, expireTime);
			msgJson.put(JSONConstants.COPY_CLIP, serverclipObject);
		}
		return msgJson;
	}


	/****************************************** CELL EDIT HISTORY ******************************************************/


}
