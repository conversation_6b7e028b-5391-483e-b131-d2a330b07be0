// $Id$
/*
 * 
 */
package com.adventnet.zoho.websheet.model;

import java.util.Iterator;

/**
 *
 * <AUTHOR>
 */
public class RangeValueIterator implements Iterator<Value>{
    RangeIterator rangeIterator;
    private final boolean isWithPattern;
    
    public RangeValueIterator(Range range, boolean skipHiddenRows, boolean isTillUsedIndex, boolean isRepeatRedundant){
        rangeIterator = new RangeIterator(range, RangeIterator.IterationStartPositionEnum.TOP_LEFT, skipHiddenRows, false, false, isTillUsedIndex, isRepeatRedundant, true);
        this.isWithPattern = true;
    }

    public RangeValueIterator(Range range, boolean skipHiddenRows, boolean isTillUsedIndex, boolean isRepeatRedundant, boolean isWithPattern){
        rangeIterator = new RangeIterator(range, RangeIterator.IterationStartPositionEnum.TOP_LEFT, skipHiddenRows, false, false, isTillUsedIndex, isRepeatRedundant, true);
        this.isWithPattern = isWithPattern;
    }
    
//    public RangeValueIterator(Sheet sheet, int startRowIndex, int startColIndex, int endRowIndex, int endColIndex, RangeIterator.IterationStartPositionEnum startPosition, boolean skipHiddenRows, boolean isTillUsedIndex, boolean isRepeatRedundant){
//        rangeIterator = new RangeIterator(sheet, startRowIndex, startColIndex, endRowIndex, endColIndex, startPosition, skipHiddenRows, false, false, isTillUsedIndex, isRepeatRedundant);
//    }

    @Override
    public boolean hasNext() {
        return rangeIterator.hasNext();
    }

    @Override
    public Value next() {
        ReadOnlyCell roCell = rangeIterator.next();
        Cell cell = roCell != null ? roCell.getCell() : null;
        Value value = cell != null ? cell.getValue() : Value.EMPTY_VALUE;
        if(this.isWithPattern) {
            value = new ValueWithPattern((ValueI) value, cell != null ? ((CellImpl) cell).getPattern(2) : null);
        }
        return value;
    }
}
